package controllers

import (
	"cwsm/infra/wsm"
	"encoding/json"
	"errors"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:c5410xfe38scc23148850be0a10c3907c2c6bf70 */
var _ = Describe("TestGetInspectiontask", func() {
	var (
		inspectionTaskReqParam struct {
			ClusterId string
		}
		patcher *gomonkey.Patches
	)

	BeforeEach(func() {
		inspectionTaskReqParam = struct{ ClusterId string }{
			ClusterId: "cluster456",
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.InspectiontaskHandler returns an error", func() {
		It("should propagate handler error", func() {
			mockErr := errors.New("task handler failure")
			patcher.ApplyFunc(wsm.Inspectiontask<PERSON><PERSON><PERSON>, func(
				keywords wsm.InspectiontaskWsmKeywords,
				_ interface{},
				_ string,
			) (map[string]interface{}, string, bool) {
				Expect(keywords.ClusterId).To(Equal(inspectionTaskReqParam.ClusterId))
				return nil, mockErr.Error(), false
			})

			result, err := GetInspectiontask(inspectionTaskReqParam.ClusterId)

			Expect(err).To(MatchError(mockErr))
			Expect(result).To(BeNil())
		})
	})

	Context("When handler fails with empty error message", func() {
		It("should generate internal error", func() {
			patcher.ApplyFunc(wsm.InspectiontaskHandler, func(
				_ wsm.InspectiontaskWsmKeywords,
				_ interface{},
				_ string,
			) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, err := GetInspectiontask(inspectionTaskReqParam.ClusterId)

			Expect(err).To(MatchError("an internal error occurred in the code"))
			Expect(result).To(BeNil())
		})
	})

	Context("When handler returns empty success response", func() {
		It("should return nil with no error", func() {
			patcher.ApplyFunc(wsm.InspectiontaskHandler, func(
				_ wsm.InspectiontaskWsmKeywords,
				_ interface{},
				_ string,
			) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, err := GetInspectiontask(inspectionTaskReqParam.ClusterId)

			Expect(err).To(BeNil())
			Expect(result).To(BeNil())
		})
	})

	Context("With empty clusterId parameter", func() {
		It("should return parameter error", func() {

			result, err := GetInspectiontask("")

			Expect(err).To(HaveOccurred())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:c5410xfe38scc23148850be0a10c3907c2c6bf70 */

/* Started by AICoder, pid:t7816m3b04s78841473a0a6df0a2af7bd1231d15 */
var _ = Describe("TestActivateInspectiontask", func() {
	var (
		reqParam wsm.InspectiontaskWsmKeywords
		reqBody  []byte
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = wsm.InspectiontaskWsmKeywords{
			ClusterId: "cluster123",
		}
		reqBody, _ = json.Marshal(map[string]interface{}{"key": "value"})
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.InspectiontaskHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.InspectiontaskHandler, func(keywords wsm.InspectiontaskWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := ActivateInspectiontask(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.InspectiontaskHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "status": "activated"}
			patcher.ApplyFunc(wsm.InspectiontaskHandler, func(keywords wsm.InspectiontaskWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := ActivateInspectiontask(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.InspectiontaskHandler returns a success status but empty error message", func() {
		It("should return an internal error", func() {
			patcher.ApplyFunc(wsm.InspectiontaskHandler, func(keywords wsm.InspectiontaskWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := ActivateInspectiontask(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(Equal(fmt.Errorf("an internal error occurred in the code")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.InspectiontaskHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.InspectiontaskHandler, func(keywords wsm.InspectiontaskWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := ActivateInspectiontask(reqParam.ClusterId, reqBody)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})

/* Ended by AICoder, pid:t7816m3b04s78841473a0a6df0a2af7bd1231d15 */
