//rdma
package controllers

import (
	"cwsm/infra/cwsmutils"
	"net/http"
)

type RdmaController struct {
	cwsmutils.Controller
}

func (e *RdmaController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetRdma(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *RdmaController) GetRdmaTopoCfg() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetRdmaTopo(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *RdmaController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateRdma(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *RdmaController) Stop() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	err := StopRdma(clusterId)
	e.actionResponse(err, http.StatusOK, nil, http.StatusBadRequest)
}

func (e *RdmaController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
