package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"net/http"
)

type SwrController struct {
	cwsmutils.Controller
}

func (e *SwrController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Infof("get swr cfg clusterId:%s", clusterId)
	data, err := GetSwr(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *SwrController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	logger.Info("get swr action response")
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
