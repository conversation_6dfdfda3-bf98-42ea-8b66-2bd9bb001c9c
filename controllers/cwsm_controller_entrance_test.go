package controllers

import (
	"testing"

	"zte.com.cn/cms/crmX/commontools/security"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/beego/beego/v2/server/web/context"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var handleXSSAttackRspPatch *gomonkey.Patches

/* Started by AICoder, pid:bb7079ed1ccc4808ab08f071fc30af1f */
func TestCwsmService(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Cwsmcontrollers Suite")
}

var _ = BeforeSuite(func() {
	handleXSSAttackRspPatch = gomonkey.ApplyFunc(security.HandleXSSAttackRSP, func(ctx *context.Context) {})
})

var _ = AfterSuite(func() {
	handleXSSAttackRspPatch.Reset()
})

/* Ended by AICoder, pid:bb7079ed1ccc4808ab08f071fc30af1f */
