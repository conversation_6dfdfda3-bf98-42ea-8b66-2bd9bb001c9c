package controllers

import (
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("SwrController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *SwrController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &SwrController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetSwr returns nil data", func() {
		It("should respond with nil data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/swr/query", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("handler failed")
			patcher.ApplyFunc(GetSwr, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

var _ = Describe("InspectController actionResponse", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *SwrController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &SwrController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When there is an error", func() {
		It("should respond with error message and fail status code", func() {
			errMessage := "some error occurred"
			patcher.ApplyMethod(reflect.TypeOf(ctrl), "ApiResponse", func(*SwrController, int, interface{}) {
				rec.WriteHeader(http.StatusBadRequest)
				rec.Write([]byte(`{"message":"` + errMessage + `"}`))
			})

			ctrl.actionResponse(errors.New(errMessage), http.StatusOK, nil, http.StatusBadRequest)

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})

	Context("When there is no error", func() {
		It("should respond with success data and success status code", func() {
			successData := map[string]string{"key": "value"}

			patcher.ApplyMethod(reflect.TypeOf(ctrl), "ApiResponse", func(*SwrController, int, interface{}) {
				rec.WriteHeader(http.StatusOK)
				rec.Write([]byte(`{"key":"value"}`))
			})

			ctrl.actionResponse(nil, http.StatusOK, successData, http.StatusBadRequest)

			Expect(rec.Code).To(Equal(http.StatusOK))
		})
	})
})
