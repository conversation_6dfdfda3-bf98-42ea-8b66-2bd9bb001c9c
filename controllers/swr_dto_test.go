package controllers

import (
	"cwsm/infra/wsm"
	"errors"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"

	. "github.com/onsi/gomega"
)

var _ = Describe("TestGetSwr", func() {
	var (
		reqParam wsm.SwrWsmKeywords
		patcher  *gomonkey.Patches
	)

	BeforeEach(func() {
		reqParam = wsm.SwrWsmKeywords{
			ClusterId: "cluster123",
		}
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.SwrHandler returns an error", func() {
		It("should return the error", func() {
			err := errors.New("handler failed")
			patcher.ApplyFunc(wsm.SwrHandler, func(keywords wsm.SwrWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, err.Error(), false
			})

			result, queryErr := GetSwr(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf(err.Error())))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.SwrHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "name": "swr1"}
			patcher.ApplyFunc(wsm.SwrHandler, func(keywords wsm.SwrWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return data, "", true
			})

			result, queryErr := GetSwr(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.SwrHandler returns a success status but empty error message", func() {
		It("should return a failed to get cluster information error", func() {
			patcher.ApplyFunc(wsm.SwrHandler, func(keywords wsm.SwrWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", false
			})

			result, queryErr := GetSwr(reqParam.ClusterId)

			Expect(queryErr).To(Equal(fmt.Errorf("failed to get cluster information or timeout")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.SwrHandler returns an empty response", func() {
		It("should return nil and no error", func() {
			patcher.ApplyFunc(wsm.SwrHandler, func(keywords wsm.SwrWsmKeywords, condition interface{}, method string) (map[string]interface{}, string, bool) {
				return nil, "", true
			})

			result, queryErr := GetSwr(reqParam.ClusterId)

			Expect(queryErr).To(BeNil())
			Expect(result).To(BeNil())
		})
	})
})
