package controllers

import (
	"cwsm/infra/cwsmutils"
	"net/http"
)

type AcceleratorController struct {
	cwsmutils.Controller
}

func (a *AcceleratorController) Get() {
	clusterId := a.Ctx.Input.Param(":clusterId")
	data, err := GetAccelerator(clusterId)
	if err != nil {
		a.ApiResponse(http.StatusBadRequest, map[string]string{"message": "get gpu info failed"})
	} else {
		a.ApiResponse(http.StatusOK, data)
	}
}

func (a *AcceleratorController) GetNodes() {
	clusterId := a.Ctx.Input.Param(":clusterId")
	data, err := GetNodes(clusterId)
	if err != nil {
		a.ApiResponse(http.StatusBadRequest, map[string]string{"message": "get node info failed"})
	} else {
		a.ApiResponse(http.StatusOK, data)
	}
}
