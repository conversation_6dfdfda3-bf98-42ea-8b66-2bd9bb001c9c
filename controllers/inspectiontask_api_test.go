package controllers

import (
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	"github.com/beego/beego/v2/server/web/context"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:b662bea0d3odb5c143ef0b6a90c8721e6e834599 */
func GetInspectiontaskBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{URL: &url.URL{
				Host: "inner-router-director:8241",
				Path: "/api/v1.0/cwsm/cluster/cluster123/apts/inspectiontask",
			}},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

/* Ended by AICoder, pid:b662bea0d3odb5c143ef0b6a90c8721e6e834599 */

/* Started by AICoder, pid:16596md203vdab1145e0095230d6d740c8d673d2 */
var _ = Describe("InspectiontaskController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectiontaskController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := GetInspectiontaskBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectiontaskController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetInspectiontask returns error", func() {
		It("should handle error response correctly", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/inspectiontask", clusterId), nil)
			ctrl.Ctx.Request = req

			err := errors.New("inspection task error")
			patcher.ApplyFunc(GetModelinspection, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			ctrl.Get()

			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))

		})
	})
})

/* Ended by AICoder, pid:16596md203vdab1145e0095230d6d740c8d673d2 */

/* Started by AICoder, pid:n6eaamd4a3m688b14f2a08a4207c971af18537e6 */
func PostInspectiontaskBeegoController() *beego.Controller {
	return &beego.Controller{
		Ctx: &context.Context{
			Request: &http.Request{
				URL: &url.URL{
					Host: "inner-router-director:8241",
					Path: "/api/v1.0/cwsm/cluster/cluster123/apts/modelinspection",
				},
			},
			Input:  &context.BeegoInput{},
			Output: &context.BeegoOutput{},
		},
		Data: map[interface{}]interface{}{},
	}
}

/* Ended by AICoder, pid:n6eaamd4a3m688b14f2a08a4207c971af18537e6 */

/* Started by AICoder, pid:42faek6cb9e0f0014e6f0beba0f18e4be363fb72 */
var _ = Describe("InspectiontaskController Post", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *InspectiontaskController
		rec            *httptest.ResponseRecorder
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})
		rec = httptest.NewRecorder()
		beegoController := PostInspectiontaskBeegoController()
		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &InspectiontaskController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When ActivateInspectiontask returns nil data", func() {
		It("should respond with 200 empty response", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1.0/cwsm/cluster/%s/apts/inspectiontask", clusterId), nil)
			ctrl.Ctx.Request = req

			patcher.ApplyFunc(ActivateInspectiontask, func(clusterId string, reqBody []byte) (interface{}, error) {
				return nil, nil
			})

			ctrl.Post()
			Expect(rec.Code).To(Equal(http.StatusOK))
			Expect(rec.Body.String()).To(Equal(``))
		})
	})
})

/* Ended by AICoder, pid:42faek6cb9e0f0014e6f0beba0f18e4be363fb72 */
