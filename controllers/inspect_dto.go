package controllers

import (
	"context"
	"cwsm/infra/constant"
	"cwsm/infra/cwsmutils"
	"cwsm/infra/wsm"
	"cwsm/models"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"
)

type InspecTaskInfo struct {
	InspectionTask json.RawMessage `json:"inspectionTask"`
}

type InspectionTaskInfo struct {
	InspectTaskName []string        `json:"inspectTaskName"`
	CCLInfo         json.RawMessage `json:"cclInfo"`
	HealthCheckInfo json.RawMessage `json:"healthCheckInfo"`
	GpuCheckInfo    json.RawMessage `json:"gpuCheckInfo"`
	RdmaInfo        json.RawMessage `json:"rdmaInfo"`
	ModelInfo       json.RawMessage `json:"modelInfo"`
}

type CreateInspectReq struct {
	Name                string          `json:"name"`
	ClusterName         string          `json:"clusterName"`
	Author              string          `json:"author"`
	Scene               string          `json:"scene"`
	CreateMode          int             `json:"createMode"`
	CreateStrategy      int             `json:"createStrategy"`
	ExecuteStrategyTime string          `json:"executeStrategyTime"`
	NodeList            []string        `json:"nodeList"`
	InspectionTask      json.RawMessage `json:"inspectionTask"`
}

type PlanInspect struct {
	Id                string          `json:"id"`
	Name              string          `json:"name"`
	ClusterId         string          `json:"clusterId"`
	ClusterName       string          `json:"clusterName"`
	Scene             string          `json:"scene"`
	NodeList          []string        `json:"nodeList"`
	Status            string          `json:"status"`
	ExecuteResult     string          `json:"executeResult"`
	ExecuteStartTime  string          `json:"executeStartTime"`
	ExecuteEndTime    string          `json:"executeEndTime"`
	ExecutePlanPerson string          `json:"executePlanPerson"`
	DesignPlanPerson  string          `json:"designPlanPerson"`
	CreateMode        int             `json:"createMode"`
	CreateStrategy    int             `json:"createStrategy"`
	InspectTaskName   []string        `json:"inspectTaskName"`
	InspectionTask    json.RawMessage `json:"inspectionTask"`
}

type InspectList struct {
	PlanList []PlanInspect `json:"planList"`
}

type InspectPlan struct {
	Id                  string    `json:"id"`
	ResultId            string    `json:"resultId"`
	PlanResultId        string    `json:"planResultId"`
	Name                string    `json:"name"`
	ClusterName         string    `json:"clusterName"`
	ClusterId           string    `json:"clusterId"`
	Author              string    `json:"author"`
	Scene               string    `json:"scene"`
	NodeList            string    `json:"nodeList"`
	Status              string    `json:"status"`
	DesignPlanPerson    string    `json:"designPlanPerson"`
	ModifyPlanPerson    string    `json:"modifyPlanPerson"`
	ExecuteResult       string    `json:"executeResult"`
	ExecuteStartTime    string    `json:"executeStartTime"`
	ExecuteEndTime      string    `json:"executeEndTime"`
	ExecutePlanPerson   string    `json:"executePlanPerson"`
	CreateTime          time.Time `json:"createTime"`
	LatestModifyTime    time.Time `json:"latestModifyTime"`
	CreateMode          int       `json:"createMode"`
	CreateStrategy      int       `json:"createStrategy"`
	ExecuteStrategyTime time.Time `json:"executeStrategyTime"`
	InspectTaskName     string    `json:"inspectTaskName"`
	InspectionTask      string    `json:"inspectionTask"`
}

type InspectPlanList struct {
	PlanList []InspectPlan `json:"planList"`
}

type DeleteInspectReq struct {
	Id []string `json:"id"`
}

type DeleteInspectResultReq struct {
	Id []string `json:"id"`
}

type ActivateInspectToWsmReq struct {
	Id                  string          `json:"id"`
	ResultId            string          `json:"resultId"`
	Name                string          `json:"name"`
	CreateMode          int             `json:"createMode"`
	Scene               string          `json:"scene"`
	NodeList            []string        `json:"nodeList"`
	CreateStrategy      int             `json:"createStrategy"`
	ExecuteStrategyTime string          `json:"executeStrategyTime"`
	InspectionTask      json.RawMessage `json:"inspectionTask"`
}

type PlanResultList struct {
	Id                  string       `json:"id"`
	Name                string       `json:"name"`
	ClusterId           string       `json:"clusterId"`
	ClusterName         string       `json:"clusterName"`
	Scene               string       `json:"scene"`
	NodeList            []string     `json:"nodeList"`
	InspectTaskName     []string     `json:"inspectTaskName"`
	CreateTime          string       `json:"createTime"`
	LatestModifyTime    string       `json:"latestModifyTime"`
	CreateMode          int          `json:"createMode"`
	CreateStrategy      int          `json:"createStrategy"`
	ExecuteStrategyTime string       `json:"executeStrategyTime"`
	DesignPlanPerson    string       `json:"designPlanPerson"`
	ModifyPlanPerson    string       `json:"modifyPlanPerson"`
	PlanResult          []PlanResult `json:"planResult"`
}

type PlanResult struct {
	Id                string `json:"id"`
	ResultId          string `json:"resultId"`
	StartTime         string `json:"startTime"`
	EndTime           string `json:"endTime"`
	ExecutePlanPerson string `json:"executePlanPerson"`
	ExecuteResult     string `json:"executeResult"`
	Status            string `json:"status"`
	CompletedCount    int    `json:"completedCount"`
	RemainingTime     int    `json:"remainingTime"`
}

type PlanResultValues struct {
	Id                string    `json:"id"`
	StartTime         time.Time `json:"startTime"`
	FinishTime        time.Time `json:"finishTime"`
	ExecutePlanPerson string    `json:"executePlanPerson"`
	ExecuteResult     string    `json:"executeResult"`
	Status            string    `json:"status"`
	CompletedCount    int       `json:"completedCount"`
	RemainingTime     int       `json:"remainingTime"`
	InspectionResult  string    `json:"inspectionResult"`
}

type InspectionPlanResult struct {
	Id                string          `json:"id"`
	PlanId            string          `json:"planId"`
	PlanName          string          `json:"planName"`
	ClusterName       string          `json:"clusterName"`
	ClusterId         string          `json:"clusterId"`
	Scene             string          `json:"scene"`
	NodeList          []string        `json:"nodeList"`
	InspectTaskName   []string        `json:"inspectTaskName"`
	StartTime         string          `json:"startTime"`
	EndTime           string          `json:"endTime"`
	ExecutePlanPerson string          `json:"executePlanPerson"`
	Status            string          `json:"status"`
	ExecuteResult     string          `json:"executeResult"`
	CompletedCount    int             `json:"completedCount"`
	RemainingTime     int             `json:"remainingTime"`
	InspectionResult  json.RawMessage `json:"inspectionResult"`
}

type InspectOnePlan struct {
	Id                  string          `json:"id"`
	Name                string          `json:"name"`
	ClusterId           string          `json:"clusterId"`
	ClusterName         string          `json:"clusterName"`
	Author              string          `json:"author"`
	Scene               string          `json:"scene"`
	ExecuteStrategyTime string          `json:"executeStrategyTime"`
	NodeList            []string        `json:"nodeList"`
	Status              string          `json:"status"`
	DesignPlanPerson    string          `json:"designPlanPerson"`
	CreateMode          int             `json:"createMode"`
	CreateStrategy      int             `json:"createStrategy"`
	InspectTaskName     []string        `json:"inspectTaskName"`
	InspectionTask      json.RawMessage `json:"inspectionTask"`
}

type ResultValues struct {
	Id               string                 `json:"id"`
	Name             string                 `json:"name"`
	ExecuteResult    string                 `json:"executeResult"`
	Status           string                 `json:"status"`
	CompletedCount   int                    `json:"completedCount"`
	InspectionResult map[string]interface{} `json:"inspectionResult"`
}

/* Started by AICoder, pid:ddc4b553c6m39aa148b508d180ad551e19f4bd1d */
func CreateInspect(clusterId string, reqBody CreateInspectReq) (string, error) {
	id := cwsmutils.NewTimeUuid("inspect-")
	resultId := cwsmutils.NewTimeUuid("inspectResult-")
	dbColData, err := convertCreateInspectReqToDbCols(reqBody, id, resultId, clusterId)
	if err != nil {
		return "", err
	}

	if !models.InsertNewResource(constant.TABLE_NAME_InspectionPlanTable, dbColData) {
		return "", errors.New("insert new resource is error")
	}

	return id, nil
}

/* Ended by AICoder, pid:ddc4b553c6m39aa148b508d180ad551e19f4bd1d */

/* Started by AICoder, pid:r3646v0574oe543145bd080af0c7a1385e88e9f2 */
func convertCreateInspectReqToDbCols(content CreateInspectReq, id string, resultId string, clusterId string) (map[string]interface{}, error) {
	currentTime, parsedTime, err := GetCreateTimeAndInspectTaskName(content)
	if err != nil {
		return nil, err
	}

	nodeListStr, inspectTaskStr, err := GetNodeListAndInspectTaskName(content)
	if err != nil {
		return nil, err
	}
	inspectCols := models.InspectionPlanCols{
		Id:                  id,
		ResultId:            resultId,
		PlanResultId:        "1",
		Name:                content.Name,
		ClusterName:         content.ClusterName,
		ClusterId:           clusterId,
		Scene:               content.Scene,
		NodeList:            nodeListStr,
		Status:              "not implemented",
		DesignPlanPerson:    content.Author,
		ModifyPlanPerson:    "",
		CreateTime:          currentTime,
		LatestModifyTime:    time.Time{},
		CreateMode:          content.CreateMode,
		CreateStrategy:      content.CreateStrategy,
		ExecuteStrategyTime: parsedTime,
		InspectTaskName:     inspectTaskStr,
		InspectionTask:      string(content.InspectionTask),
	}

	data, err := cwsmutils.Struct2Map(inspectCols)
	if err != nil {
		return nil, err
	}

	return data, nil
}

/* Ended by AICoder, pid:r3646v0574oe543145bd080af0c7a1385e88e9f2 */

/* Started by AICoder, pid:62627k2b27c57a3148d70a798062f01ea8918ca7 */
func GetCreateTimeAndInspectTaskName(content CreateInspectReq) (time.Time, time.Time, error) {
	currentTime := time.Now()
	layout := time.RFC3339

	parsedTime, err := time.Parse(layout, content.ExecuteStrategyTime)
	if err != nil {
		return time.Time{}, time.Time{}, err
	}

	return currentTime, parsedTime, nil
}

/* Ended by AICoder, pid:62627k2b27c57a3148d70a798062f01ea8918ca7 */

/* Started by AICoder, pid:507577a766dce8514a3f090bc0e84a2c08f7497d */
func GetNodeListAndInspectTaskName(content CreateInspectReq) (string, string, error) {
	var taskInfo InspectionTaskInfo
	if err := json.Unmarshal(content.InspectionTask, &taskInfo); err != nil {
		return "", "", err
	}
	inspectTaskStr, err := cwsmutils.SliceToString(taskInfo.InspectTaskName)
	if err != nil {
		return "", "", err
	}

	var nodeListStr string

	if content.NodeList != nil {
		nodeListStr, err = cwsmutils.SliceToString(content.NodeList)
		if err != nil {
			return "", "", err
		}
	} else {
		nodeListStr = ""
	}

	return nodeListStr, inspectTaskStr, nil
}

/* Ended by AICoder, pid:507577a766dce8514a3f090bc0e84a2c08f7497d */

/* Started by AICoder, pid:8879171bf5caf6414c5909f11057cd5d45f0b037 */
func ModifyInspect(clusterId string, id string, reqBody CreateInspectReq) (string, error) {
	currentTime, parsedTime, err := GetCreateTimeAndInspectTaskName(reqBody)
	if err != nil {
		return "", err
	}

	nodeListStr, inspectTaskStr, err := GetNodeListAndInspectTaskName(reqBody)
	if err != nil {
		return "", err
	}

	inspectionPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		logger.Errorf("query resource by id failed: %v", err)
		return "", err
	}

	inspectCols := models.InspectionPlanCols{
		Id:                  id,
		ResultId:            inspectionPlan["resultId"].(string),
		PlanResultId:        inspectionPlan["planResultId"].(string),
		Name:                reqBody.Name,
		ClusterName:         reqBody.ClusterName,
		ClusterId:           clusterId,
		Scene:               reqBody.Scene,
		NodeList:            nodeListStr,
		Status:              inspectionPlan["status"].(string),
		DesignPlanPerson:    reqBody.Author,
		ModifyPlanPerson:    reqBody.Author,
		CreateTime:          inspectionPlan["createTime"].(time.Time),
		LatestModifyTime:    currentTime,
		CreateMode:          reqBody.CreateMode,
		CreateStrategy:      reqBody.CreateStrategy,
		ExecuteStrategyTime: parsedTime,
		InspectTaskName:     inspectTaskStr,
		InspectionTask:      string(reqBody.InspectionTask),
	}

	inspectColsMap, err := cwsmutils.Struct2Map(inspectCols)
	if err != nil {
		return "", err
	}

	if !models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, id, inspectColsMap) {
		logger.Errorf("insert plan resource failed: %v", err)
		return "", err
	}

	return id, nil
}

/* Ended by AICoder, pid:8879171bf5caf6414c5909f11057cd5d45f0b037 */

/* Started by AICoder, pid:hbf04od142l5d971461709d44077522d6fb05060 */
func GetNodeListStrAndInspectTaskNameStr(planList InspectPlan) ([]string, []string, error) {
	inspectTaskName, err := cwsmutils.StringToSlice(planList.InspectTaskName)
	if err != nil {
		return nil, nil, err
	}

	var nodeList []string
	if planList.NodeList != "" {
		nodeList, err = cwsmutils.StringToSlice(planList.NodeList)
		if err != nil {
			return nil, nil, err
		}
	} else {
		nodeList = []string{}
	}

	return nodeList, inspectTaskName, nil
}

/* Ended by AICoder, pid:hbf04od142l5d971461709d44077522d6fb05060 */

/* Started by AICoder, pid:debf4fcc9858dfd14886087ae0c3da1d0894be01 */
func GetPlanList(inspectionPlan []map[string]interface{}, inspectPlanList *InspectPlanList) error {
	jsonData, err := json.Marshal(inspectionPlan)
	if err != nil {
		logger.Errorf("error inspectionplan marshaling JSON: %v", err)
		return err
	}

	err = json.Unmarshal(jsonData, &inspectPlanList.PlanList)
	if err != nil {
		return fmt.Errorf("error inspectlist planlist marshaling JSON: %v", err)
	}

	return nil
}

/* Ended by AICoder, pid:debf4fcc9858dfd14886087ae0c3da1d0894be01 */

/* Started by AICoder, pid:q508die756w6bbb14d700a6300fcdf1e6f43bef0 */
func GetFinishTime(inspectReslut *PlanResultValues, planList *InspectPlan) (string, error) {
	finishTime := inspectReslut.FinishTime.Format(time.RFC3339)
	if planList.Status == "finished" || planList.Status == "stopped" {
		return finishTime, nil
	} else {
		if finishTime == "0001-01-01T00:00:00Z" {
			finishTime = ""
			return finishTime, nil
		}
	}
	return "", nil
}

/* Ended by AICoder, pid:q508die756w6bbb14d700a6300fcdf1e6f43bef0 */

/* Started by AICoder, pid:u3b80m2bbai43be145d20a5110de481b06b3a088 */
func GetStartTimeAndFinishTime(inspectionResult map[string]interface{}) (time.Time, time.Time, error) {
	startTime, ok := inspectionResult["startTime"].(time.Time)
	if !ok {
		return time.Time{}, time.Time{}, fmt.Errorf("startTime error")
	}

	finishTime, ok := inspectionResult["finishTime"].(time.Time)
	if !ok {
		return time.Time{}, time.Time{}, fmt.Errorf("finishTime error")
	}

	return startTime, finishTime, nil
}

/* Ended by AICoder, pid:u3b80m2bbai43be145d20a5110de481b06b3a088 */

/* Started by AICoder, pid:x5c42a227eh5bcb14c0a080e000069178c847066 */
func StartUpdateRunningPlan(ctx context.Context) {
	logger.Infof("start update running plan")
	timer := time.NewTicker(10 * time.Second)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Infof("update running plan failed")
			return
		case <-timer.C:
			UpdateRunningPlan(ctx)
		}
	}
}

/* Ended by AICoder, pid:x5c42a227eh5bcb14c0a080e000069178c847066 */

/* Started by AICoder, pid:o89abe5b43m9bdc148a9090590cad954601121dc */
func UpdateRunningPlan(ctx context.Context) {
	inspectionPlan, err := models.QueryResourceAll(constant.TABLE_NAME_InspectionPlanTable)
	if err != nil {
		logger.Errorf("query inspection plan is failed: %v", err)
	}

	var inspectPlanList InspectPlanList

	errGetPlanList := GetPlanList(inspectionPlan, &inspectPlanList)
	if errGetPlanList != nil {
		logger.Errorf("error inspectlist planlist marshaling json: %v", errGetPlanList)
	}

	var wg sync.WaitGroup

	for _, planList := range inspectPlanList.PlanList {
		inspectionResult, _, _, err := GetValuesOfInspectionResult(planList)
		if err != nil {
			logger.Errorf("error getting values of inspection result: %v", err)
			continue
		}
		status := planList.Status
		if inspectionResult != nil && status == "running" {
			wg.Add(1)
			go func(pl InspectPlan) {
				defer wg.Done()
				getValuesFromWsm(pl.Id, pl.PlanResultId, pl.ClusterId, pl.CreateTime, pl.Author)
				errGetInspectResult := GetInspectResult(pl.ClusterId, pl.Id, pl.PlanResultId, pl.CreateTime, pl.Author)
				if errGetInspectResult != nil {
					logger.Errorf("error get inspect result: %v", errGetInspectResult)
				}
			}(planList)
		}
	}
	wg.Wait()
}

/* Ended by AICoder, pid:o89abe5b43m9bdc148a9090590cad954601121dc */

/* Started by AICoder, pid:o3bab37a91757a61460f0a17f0c122668288424c */
func GetInspect(clusterId string) (interface{}, error) {
	condtion := map[string]interface{}{"clusterId": clusterId}
	inspectionPlan, err := models.QueryResourceByCondition(constant.TABLE_NAME_InspectionPlanTable, condtion)
	if err != nil {
		return nil, err
	}

	if inspectionPlan != nil {
		var inspectList InspectList
		var inspectPlanList InspectPlanList

		errGetPlanList := GetPlanList(inspectionPlan, &inspectPlanList)
		if errGetPlanList != nil {
			logger.Errorf("error inspectlist planlist marshaling json: %v", err)
			return nil, errGetPlanList
		}

		for _, planList := range inspectPlanList.PlanList {

			inspectionResult, nodeList, inspectTaskName, err := GetValuesOfInspectionResult(planList)
			if err != nil {
				return nil, err
			}

			if inspectionResult != nil && (planList.Status == "finished" || planList.Status == "stopped" || planList.Status == "running") {
				inspectResult, finishTime, err := GetInspectResultValues(inspectionResult, planList)
				if err != nil {
					return nil, err
				}
				plan := PlanInspect{
					Id:                planList.Id,
					Name:              planList.Name,
					ClusterId:         planList.ClusterId,
					ClusterName:       planList.ClusterName,
					Scene:             planList.Scene,
					NodeList:          nodeList,
					Status:            inspectionResult["status"].(string),
					ExecuteResult:     inspectionResult["executeResult"].(string),
					ExecuteStartTime:  inspectResult.StartTime.Format(time.RFC3339),
					ExecuteEndTime:    finishTime,
					ExecutePlanPerson: inspectionResult["executePlanPerson"].(string),
					DesignPlanPerson:  planList.DesignPlanPerson,
					CreateMode:        planList.CreateMode,
					CreateStrategy:    planList.CreateStrategy,
					InspectTaskName:   inspectTaskName,
					InspectionTask:    json.RawMessage(planList.InspectionTask),
				}
				inspectList.PlanList = append(inspectList.PlanList, plan)
			} else {
				plan, err := GetPlanValuesNoResultTable(planList, nodeList, inspectTaskName)
				if err != nil {
					return nil, fmt.Errorf("get plan values no result failed :%v", err)
				}

				inspectList.PlanList = append(inspectList.PlanList, plan)
			}
		}

		data, err := cwsmutils.Struct2Map(inspectList)
		if err != nil {
			logger.Errorf("inspectlist :%v", err)
			return nil, err
		}

		return data, nil
	}
	return nil, nil
}

/* Ended by AICoder, pid:o3bab37a91757a61460f0a17f0c122668288424c */

/* Started by AICoder, pid:l640f47430r80cd1425c09aba0af8c2886028be3 */
func GetValuesOfInspectionResult(planList InspectPlan) (map[string]interface{}, []string, []string, error) {
	nodeList, inspectTaskName, err := GetNodeListStrAndInspectTaskNameStr(planList)
	if err != nil {
		return nil, nil, nil, err
	}

	var inspectionResult map[string]interface{}

	if planList.PlanResultId != "1" {
		inspectionResult, err = models.QueryResourceById(constant.TABLE_NAME_InspectionResultTable, planList.PlanResultId)
		if err != nil {
			return nil, nil, nil, nil
		}
	} else {
		inspectionResult, err = models.QueryResourceByResultId(constant.TABLE_NAME_InspectionResultTable, planList.ResultId)
		if err != nil {
			return nil, nil, nil, nil
		}
	}

	return inspectionResult, nodeList, inspectTaskName, nil
}

/* Ended by AICoder, pid:l640f47430r80cd1425c09aba0af8c2886028be3 */

/* Started by AICoder, pid:744ce848f7243ff149b90933a067121d01f6d019 */
func GetInspectResultValues(inspectionResult map[string]interface{}, planList InspectPlan) (*PlanResultValues, string, error) {
	var inspectResult PlanResultValues

	errGetResultValue := GetResultValue(inspectionResult, &inspectResult)
	if errGetResultValue != nil {
		logger.Errorf("error inspectlist planlist marshaling json: %v", errGetResultValue)
		return nil, "", errGetResultValue
	}

	finishTime, err := GetFinishTime(&inspectResult, &planList)
	if err != nil {
		return nil, "", err
	}

	return &inspectResult, finishTime, nil
}

/* Ended by AICoder, pid:744ce848f7243ff149b90933a067121d01f6d019 */

/* Started by AICoder, pid:y0d7e6fb48m286114e0108cbf0d2322615528b6a */
func GetPlanValuesNoResultTable(planList InspectPlan, nodeList []string, inspectTaskName []string) (PlanInspect, error) {
	plan := PlanInspect{
		Id:                planList.Id,
		Name:              planList.Name,
		ClusterId:         planList.ClusterId,
		ClusterName:       planList.ClusterName,
		Scene:             planList.Scene,
		NodeList:          nodeList,
		Status:            planList.Status,
		ExecuteResult:     planList.ExecuteResult,
		ExecuteStartTime:  planList.ExecuteStartTime,
		ExecuteEndTime:    planList.ExecuteEndTime,
		ExecutePlanPerson: planList.ExecutePlanPerson,
		DesignPlanPerson:  planList.DesignPlanPerson,
		CreateMode:        planList.CreateMode,
		CreateStrategy:    planList.CreateStrategy,
		InspectTaskName:   inspectTaskName,
		InspectionTask:    json.RawMessage(planList.InspectionTask),
	}
	return plan, nil
}

/* Ended by AICoder, pid:y0d7e6fb48m286114e0108cbf0d2322615528b6a */

func GetPlanValue(inspectionPlan map[string]interface{}, inspectPlan *InspectPlan) error {
	jsonData, err := json.Marshal(inspectionPlan)
	if err != nil {
		return fmt.Errorf("error inspectionplan marshaling json: %v", err)
	}

	err = json.Unmarshal(jsonData, &inspectPlan)
	if err != nil {
		return fmt.Errorf("error planinspect marshaling json: %v", err)
	}
	return nil
}

func GetResultValue(inspectionResult map[string]interface{}, inspectReslut *PlanResultValues) error {
	jsonData, err := json.Marshal(inspectionResult)
	if err != nil {
		return fmt.Errorf("error inspectionplan marshaling json: %v", err)
	}

	err = json.Unmarshal(jsonData, &inspectReslut)
	if err != nil {
		return fmt.Errorf("error planinspect marshaling json: %v", err)
	}

	return nil
}

func GetOneInspect(clusterId string, id string) (interface{}, error) {
	inspectionPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		return nil, err
	}

	if inspectionPlan != nil {
		var inspectPlan InspectPlan

		errGetPlanList := GetPlanValue(inspectionPlan, &inspectPlan)
		if errGetPlanList != nil {
			logger.Errorf("error inspectlist planlist marshaling json: %v", err)
			return nil, errGetPlanList
		}

		nodeList, inspectTaskName, err := GetNodeListStrAndInspectTaskNameStr(inspectPlan)
		if err != nil {
			return nil, err
		}

		_, inspectionTask, err := GetexecuteStrategyTimeAndinspectionTask(inspectionPlan)
		if err != nil {
			return nil, err
		}

		plan := InspectOnePlan{
			Id:                  inspectPlan.Id,
			Name:                inspectPlan.Name,
			ClusterId:           clusterId,
			ClusterName:         inspectPlan.ClusterName,
			Author:              inspectPlan.Author,
			Scene:               inspectPlan.Scene,
			ExecuteStrategyTime: inspectPlan.ExecuteStrategyTime.Format(time.RFC3339),
			NodeList:            nodeList,
			Status:              inspectPlan.Status,
			DesignPlanPerson:    inspectPlan.DesignPlanPerson,
			CreateMode:          inspectPlan.CreateMode,
			CreateStrategy:      inspectPlan.CreateStrategy,
			InspectTaskName:     inspectTaskName,
			InspectionTask:      json.RawMessage(inspectionTask),
		}

		data, err := cwsmutils.Struct2Map(plan)
		if err != nil {
			logger.Errorf("inspectlist is failed:%v", err)
			return nil, err
		}

		return data, nil
	}
	return nil, nil
}

/* Started by AICoder, pid:hc83318d1ckc7da140d70b5880c45711f3e5ca89 */
func GetexecuteStrategyTimeAndinspectionTask(inspectionPlan map[string]interface{}) (time.Time, string, error) {
	executeStrategyTime, ok := inspectionPlan["executeStrategyTime"].(time.Time)
	if !ok {
		return time.Time{}, "", fmt.Errorf("executeStrategyTime error")
	}

	inspectionTask, ok := inspectionPlan["inspectionTask"].(string)
	if !ok {
		return time.Time{}, "", fmt.Errorf("inspectionTask error")
	}

	return executeStrategyTime, inspectionTask, nil
}

/* Ended by AICoder, pid:hc83318d1ckc7da140d70b5880c45711f3e5ca89 */

func GetActivateInspectToWsmReq(inspectionPlan map[string]interface{}, resultId string) (map[string]interface{}, *InspectPlan, error) {
	var inspectPlan InspectPlan

	errGetPlanList := GetPlanValue(inspectionPlan, &inspectPlan)
	if errGetPlanList != nil {
		logger.Errorf("error inspectlist planlist marshaling json: %v", errGetPlanList)
		return nil, nil, errGetPlanList
	}

	nodeList, err := cwsmutils.StringToSlice(inspectPlan.NodeList)
	if err != nil {
		return nil, nil, err
	}

	executeStrategyTime, inspectionTask, err := GetexecuteStrategyTimeAndinspectionTask(inspectionPlan)
	if err != nil {
		return nil, nil, err
	}

	activatePlan := ActivateInspectToWsmReq{
		Id:                  inspectPlan.Id,
		ResultId:            resultId,
		Name:                inspectPlan.Name,
		CreateMode:          inspectPlan.CreateMode,
		Scene:               inspectPlan.Scene,
		NodeList:            nodeList,
		CreateStrategy:      inspectPlan.CreateStrategy,
		ExecuteStrategyTime: executeStrategyTime.Format(time.RFC3339),
		InspectionTask:      json.RawMessage(inspectionTask),
	}

	activateData, err := cwsmutils.Struct2Map(activatePlan)
	if err != nil {
		logger.Errorf("inspectlist :%v", err)
		return nil, nil, err
	}

	return activateData, &inspectPlan, nil

}

func ActivateInspect(clusterId string, id string, author string) error {
	inspectionPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		return err
	}

	if inspectionPlan != nil {

		resultId := cwsmutils.NewTimeUuid("result-")

		activateData, inspectPlan, err := GetActivateInspectToWsmReq(inspectionPlan, resultId)
		if err != nil {
			logger.Errorf("get activate inspect to wsm req is failed :%v", err)
			return err
		}
		currentTime := time.Now()

		_, inspectErr, suc := wsm.InspectHandler(clusterId, activateData, "post", "")
		if !suc {
			if inspectErr == "" {
				return fmt.Errorf("an internal error occurred in the code")
			} else {
				return fmt.Errorf(inspectErr)
			}
		}

		errInspectionResult := InspectionResult(inspectionPlan, inspectPlan.ResultId, currentTime, author, resultId)
		if errInspectionResult != nil {
			logger.Errorf("err inspection result err :%v", errInspectionResult)
			return errInspectionResult
		}

		status := "running"
		errUpdatePlanStatus := UpdatePlanStatus(inspectPlan.Id, status)
		if errUpdatePlanStatus != nil {
			return errUpdatePlanStatus
		}

		errGetInspectResult := GetInspectResult(clusterId, id, resultId, currentTime, author)
		if errGetInspectResult != nil {
			logger.Errorf("error get inspect result: %v", errGetInspectResult)
			return errGetInspectResult
		}

		go func() {
			getValuesFromWsm(id, resultId, clusterId, currentTime, author)
			errGetInspectResult := GetInspectResult(clusterId, id, resultId, currentTime, author)
			if errGetInspectResult != nil {
				if errGetInspectResult.Error() == "plan not found" {
					return
				}
				logger.Errorf("error get inspect result: %v", errGetInspectResult)
				return
			}
		}()
	}
	return nil
}

func UpdateStatusForPlanAndResultTable(id string, resultId string) error {
	updateData := map[string]interface{}{
		"status":        "finished",
		"executeResult": "fail",
		"finishTime":    time.Now(),
	}

	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, resultId, updateData); !suc {
		logger.Errorf("failed to update inspection result status for id: %s", resultId)
		return nil
	}

	logger.Infof("finished UpdateResourceById TABLE_NAME_InspectionResultTable")

	inspectionNewPlans, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		logger.Errorf("failed to get inspection plan")
		return nil
	}

	logger.Infof("inspectionNewPlans TABLE_NAME_InspectionPlanTable id :%d inspectionNewPlans:%v", id, inspectionNewPlans)

	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, id, map[string]interface{}{"status": "finished"}); !suc {
		logger.Errorf("failed to update inspection result status for id: %s", id)
		return nil
	}

	logger.Infof("finished UpdateResourceById TABLE_NAME_InspectionPlanTable")
	return nil
}

/* Started by AICoder, pid:309ea106aemb71e1474e0aa9b0adff5675f2f938 */
func getValuesFromWsm(id string, resultId string, clusterId string, currentTime time.Time, author string) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Minute)
	defer cancel()

	var wg sync.WaitGroup

	wg.Add(1)
	go func() {
		defer wg.Done()
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				if ctx.Err() == context.DeadlineExceeded {
					errUpdateStatusForPlanAndResultTable := UpdateStatusForPlanAndResultTable(id, resultId)
					if errUpdateStatusForPlanAndResultTable != nil {
						logger.Errorf("failed to update status for plan and result table")
						continue
					}
					logger.Infof("inspection task %s timed out after 3 minutes, status updated", resultId)
				}
				return
			case <-ticker.C:
				resultStatus, err := GetResultStatus(id, resultId)
				if err != nil {
					logger.Errorf("failed to get result status")
					continue
				}
				if resultStatus == "stopped" || resultStatus == "finished" {
					cancel()
					return
				}
			}
		}
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		getInspectResult(clusterId, id, resultId, currentTime, author, ctx)
	}()

	<-ctx.Done()
	logger.Infof("exiting the main goroutine due to context cancellation")

	wg.Wait()
	logger.Infof("all goroutines finished")
}

/* Ended by AICoder, pid:309ea106aemb71e1474e0aa9b0adff5675f2f938 */

func GetResultStatus(id string, resultId string) (string, error) {
	inspectionNewPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		logger.Errorf("failed to get inspection plan")
		return "", nil
	}

	logger.Infof("inspectionNewPlaninspectionNewPlaninspectionNewPlan")

	_, ok := inspectionNewPlan["status"].(string)
	if !ok {
		logger.Errorf("failed to get plan status")
		return "", nil
	}

	logger.Infof("inspectionNewPlanstatusstatusstatusstatus")

	inspectionResult, err := models.QueryResourceById(constant.TABLE_NAME_InspectionResultTable, resultId)
	if err != nil {
		logger.Errorf("failed to get inspection result")
		return "", nil
	}

	logger.Infof("inspectionResultinspectionResultinspectionResult")

	resultStatus, ok := inspectionResult["status"].(string)
	if !ok {
		logger.Errorf("failed to get result status")
		return "", nil
	}

	logger.Infof("resultStatusresultStatusresultStatus :%s", resultStatus)

	return resultStatus, nil
}

/* Started by AICoder, pid:8820bmfe92o9fbe14cfd0a75a05eb7160f49b0e2 */
func getInspectResult(clusterId string, id string, resultId string, currentTime time.Time, author string, ctx context.Context) {
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			errGetInspectResult := GetInspectResult(clusterId, id, resultId, currentTime, author)
			if errGetInspectResult != nil {
				if errGetInspectResult.Error() == "plan not found" {
					errUpdateStatusForPlanAndResultTable := UpdateStatusForPlanAndResultTable(id, resultId)
					if errUpdateStatusForPlanAndResultTable != nil {
						logger.Errorf("failed to update status for plan and result table")
						continue
					}
					if err := UpdateRunningStatusToDisconnected(resultId); err != nil {
						logger.Errorf("failed to update running status to disconnected: %v", err)
						continue
					}
					return
				}
				logger.Errorf("error get inspect result: %v", errGetInspectResult)
				continue
			}
		}
	}
}

/* Ended by AICoder, pid:8820bmfe92o9fbe14cfd0a75a05eb7160f49b0e2 */

func InspectionResult(inspectionPlan map[string]interface{}, resultId string, currentTime time.Time, author string, id string) error {
	errUpdatePlanResultId := UpdatePlanResultId(inspectionPlan["id"].(string), id)
	if errUpdatePlanResultId != nil {
		return errUpdatePlanResultId
	}

	inspectCols := models.InspectionResultCols{
		Id:                id,
		ResultId:          resultId,
		PlanId:            inspectionPlan["id"].(string),
		ExecuteResult:     "not implemented",
		Status:            "",
		StartTime:         currentTime,
		FinishTime:        time.Time{},
		ExecutePlanPerson: author,
		CompletedCount:    0,
		RemainingTime:     0,
		InspectionResult:  "",
	}

	data, err := cwsmutils.Struct2Map(inspectCols)
	if err != nil {
		return err
	}

	if !models.InsertNewResource(constant.TABLE_NAME_InspectionResultTable, data) {
		return errors.New("insert new resource is error")
	}

	return nil
}

/* Started by AICoder, pid:nf27dia10ahc03b14dea0a84d0f7122c5e34f7c0 */
func GetFinishTimeAndExecuteResult(status string, resultValues *ResultValues) (time.Time, string, error) {
	var finishTime time.Time

	if status == "finished" || status == "stopped" {
		currentResultTime := time.Now()
		finishTime = currentResultTime
	} else {
		finishTime = time.Time{}
	}

	var executeResult string

	if status == "running" || status == "not implemented" {
		executeResult = "not implemented"
	} else {
		executeResult = resultValues.ExecuteResult
	}

	return finishTime, executeResult, nil
}

/* Ended by AICoder, pid:nf27dia10ahc03b14dea0a84d0f7122c5e34f7c0 */

func GetInspectResult(clusterId string, planId string, id string, currentTime time.Time, author string) error {
	inspectResult, inspectErr, suc := wsm.InspectHandler(clusterId, nil, "get", planId)
	if !suc {
		if inspectErr == "" && inspectResult != nil {
			if errMsg, ok := inspectResult["err"].(string); ok && errMsg == "plan not found" {
				return fmt.Errorf("plan not found")
			}
			return fmt.Errorf("an internal error occurred in the code")

		} else {
			return fmt.Errorf(inspectErr)
		}
	}

	var resultValues ResultValues

	errGetPlanResult := GetResultValues(inspectResult, &resultValues)
	if errGetPlanResult != nil {
		logger.Errorf("error inspectlist planlist marshaling json: %v", errGetPlanResult)
		return errGetPlanResult
	}

	status := resultValues.Status

	finishTime, executeResult, err := GetFinishTimeAndExecuteResult(status, &resultValues)
	if err != nil {
		return fmt.Errorf("get finish time and executeresult is failed: %v", err)
	}

	inspectionResultMap := inspectResult["inspectionResult"].(map[string]interface{})
	inspectionResultJSON, err := json.Marshal(inspectionResultMap)
	if err != nil {
		return fmt.Errorf("failed to marshal inspectionresult: %v", err)
	}

	errUpdateStatus := UpdateStatus(inspectResult, id, status, executeResult)
	if errUpdateStatus != nil {
		return errUpdateStatus
	}

	errUpdateResult := UpdateResult(id, finishTime, inspectionResultJSON)
	if errUpdateResult != nil {
		return errUpdateResult
	}

	return nil
}

func UpdateStartTimeAndEndTime(id string, finishTime time.Time, startTime time.Time) error {
	errUpdateFinishTimeValues := UpdateFinishTimeValues(id, finishTime)
	if errUpdateFinishTimeValues != nil {
		return errUpdateFinishTimeValues
	}

	errUpdateStartTimeValues := UpdateStartTimeValues(id, startTime)
	if errUpdateStartTimeValues != nil {
		return errUpdateStartTimeValues
	}

	return nil
}

/* Started by AICoder, pid:peb7af4cfbt65fe14b34080cd074ae2e49200331 */
func UpdateStatus(inspectResult map[string]interface{}, id string, status string, executeResult string) error {
	errUpdatePlanStatus := UpdatePlanStatus(inspectResult["id"].(string), status)
	if errUpdatePlanStatus != nil {
		return errUpdatePlanStatus
	}

	errUpdateResultStatus := UpdateResultStatus(id, status)
	if errUpdateResultStatus != nil {
		return errUpdateResultStatus
	}

	if executeResult != "not implemented" {
		errUpdateExecuteResultStatus := UpdateExecuteResultStatus(id, executeResult)
		if errUpdateExecuteResultStatus != nil {
			return errUpdateExecuteResultStatus
		}
	}

	return nil
}

/* Ended by AICoder, pid:peb7af4cfbt65fe14b34080cd074ae2e49200331 */

/* Started by AICoder, pid:8ab82i9540f941f14f710862402c1e15909369b3 */
func UpdateResult(id string, finishTime time.Time, inspectionResultJSON []byte) error {
	errUpdateFinishTimeValues := UpdateFinishTimeValues(id, finishTime)
	if errUpdateFinishTimeValues != nil {
		return errUpdateFinishTimeValues
	}

	errUpdateInspectionResultValues := UpdateInspectionResultValues(id, string(inspectionResultJSON))
	if errUpdateInspectionResultValues != nil {
		return errUpdateInspectionResultValues
	}

	return nil
}

/* Ended by AICoder, pid:8ab82i9540f941f14f710862402c1e15909369b3 */

/* Started by AICoder, pid:hb8797e868ob09c14c9709c230f6191f1da87891 */
func UpdateResultTableValues(id string, status string, finishTime time.Time, inspectionResultJSON []byte) error {
	errUpdateResultStatus := UpdateResultStatus(id, status)
	if errUpdateResultStatus != nil {
		return errUpdateResultStatus
	}

	errUpdateFinishTimeValues := UpdateFinishTimeValues(id, finishTime)
	if errUpdateFinishTimeValues != nil {
		return errUpdateFinishTimeValues
	}

	errUpdateInspectionResultValues := UpdateInspectionResultValues(id, string(inspectionResultJSON))
	if errUpdateInspectionResultValues != nil {
		return errUpdateInspectionResultValues
	}

	return nil
}

/* Ended by AICoder, pid:hb8797e868ob09c14c9709c230f6191f1da87891 */

/* Started by AICoder, pid:q5b4736266b83ad1457b0a32005ced0760d86f4f */
func UpdatePlanExecuteResult(planId string, executeResult string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"executeResult": executeResult}); suc {
		logger.Debug("updateplanstatus %s executeresult to %s", planId, executeResult)
		return nil
	}
	logger.Errorf("update db evaluate %s executeresult %s failed", planId, executeResult)
	return nil
}

/* Ended by AICoder, pid:q5b4736266b83ad1457b0a32005ced0760d86f4f */

/* Started by AICoder, pid:y3398z0f6bz474014b860a20a0d8990150e81000 */
func UpdatePlanExecuteStartTime(planId string, executeStartTime time.Time) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"executeStartTime": executeStartTime}); suc {
		logger.Debug("updateplanstatus %s executestarttime to %s", planId, executeStartTime)
		return nil
	}
	logger.Errorf("update db evaluate %s executestarttime %s failed", planId, executeStartTime)
	return nil
}

/* Ended by AICoder, pid:y3398z0f6bz474014b860a20a0d8990150e81000 */

/* Started by AICoder, pid:bfb2co88405dfc9147310b73a0d1b50a84d8681e */
func UpdatePlanExecuteEndTime(planId string, executeEndTime time.Time) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"executeEndTime": executeEndTime}); suc {
		logger.Debug("updateplanstatus %s executeendtime to %s", planId, executeEndTime)
		return nil
	}
	logger.Errorf("update db evaluate %s executeendtime %s failed", planId, executeEndTime)
	return nil
}

/* Ended by AICoder, pid:bfb2co88405dfc9147310b73a0d1b50a84d8681e */

/* Started by AICoder, pid:l570dv7f1b30d5514eda09c5c0a3150688f823e4 */
func UpdatePlanExecutePlanPerson(planId string, executePlanPerson string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"executePlanPerson": executePlanPerson}); suc {
		logger.Debug("updateplanstatus %s executeplanperson to %s", planId, executePlanPerson)
		return nil
	}
	logger.Errorf("update db evaluate %s executeplanperson %s failed", planId, executePlanPerson)
	return nil
}

/* Ended by AICoder, pid:l570dv7f1b30d5514eda09c5c0a3150688f823e4 */

func UpdatePlanStatus(planId string, status string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"status": status}); suc {
		logger.Debug("updateplanstatus %s status to %s", planId, status)
		return nil
	}
	logger.Errorf("update db evaluate %s status %s failed", planId, status)
	return nil
}

func UpdatePlanResultId(planId string, planResultId string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"planResultId": planResultId}); suc {
		logger.Debug("updateplanstatus %s planresultid to %s", planId, planResultId)
		return nil
	}
	logger.Errorf("update db evaluate %s planresultid %s failed", planId, planResultId)
	return nil
}

func UpdateResultStatus(planId string, status string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, planId, map[string]interface{}{"status": status}); suc {
		logger.Debug("updateplanstatus %s status to %s", planId, status)
		return nil
	}
	logger.Errorf("update db evaluate %s status %s failed", planId, status)
	return nil
}

func UpdateExecuteResultStatus(planId string, executeResult string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, planId, map[string]interface{}{"executeResult": executeResult}); suc {
		return nil
	}
	logger.Errorf("update db update execute result status %s status %s failed", planId, executeResult)
	return nil
}

func UpdateFinishTimeValues(id string, finishTime time.Time) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, id, map[string]interface{}{"finishTime": finishTime}); suc {
		return nil
	}
	logger.Errorf("update db update finish time values %s values %v failed", id, finishTime)
	return nil
}

func UpdateResultId(planId string, planResultId string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionPlanTable, planId, map[string]interface{}{"planResultId": planResultId}); suc {
		return nil
	}
	logger.Errorf("update db update execute result status %s status %s failed", planId, planResultId)
	return nil
}

func UpdateInspectionResultValues(id string, inspectionResult string) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, id, map[string]interface{}{"inspectionResult": inspectionResult}); suc {
		return nil
	}
	logger.Errorf("update db update inspection result values %s values %v failed", id, inspectionResult)
	return nil
}

/* Started by AICoder, pid:60114l1fee2c133143eb0b7d900fd502c608be55 */
func UpdateStopInspectionResultValues(id string, inspectionResult string) error {
	if suc := models.UpdateResourceByResultId(constant.TABLE_NAME_InspectionResultTable, id, map[string]interface{}{"inspectionResult": inspectionResult}); suc {
		return nil
	}
	logger.Errorf("update db update inspectionresultvalues %s values %v failed", id, inspectionResult)
	return nil
}

/* Ended by AICoder, pid:60114l1fee2c133143eb0b7d900fd502c608be55 */

/* Started by AICoder, pid:vda498421bcdd93147600917e04c7d0adf18d952 */
func UpdateStartTimeValues(id string, startTime time.Time) error {
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, id, map[string]interface{}{"startTime": startTime}); suc {
		return nil
	}
	logger.Errorf("update db update starttimevalues %s values %v failed", id, startTime)
	return nil
}

/* Ended by AICoder, pid:vda498421bcdd93147600917e04c7d0adf18d952 */

/* Started by AICoder, pid:k046ea8fe3ff81814e6a0be3806a560793a84c34 */
func UpdateStopResultStatus(planId string, status string) error {
	if suc := models.UpdateResourceByResultId(constant.TABLE_NAME_InspectionResultTable, planId, map[string]interface{}{"status": status}); suc {
		logger.Debug("updateplanstatus %s status to %s", planId, status)
		return nil
	}
	logger.Errorf("update db evaluate %s status %s failed", planId, status)
	return nil
}

/* Ended by AICoder, pid:k046ea8fe3ff81814e6a0be3806a560793a84c34 */

/* Started by AICoder, pid:r66ea26be852726143a908c7c04a400ebd184bb8 */
func UpdateStopExecuteResultStatus(planId string, executeResult string) error {
	if suc := models.UpdateResourceByResultId(constant.TABLE_NAME_InspectionResultTable, planId, map[string]interface{}{"executeResult": executeResult}); suc {
		return nil
	}
	logger.Errorf("update db update executeresultstatus %s status %s failed", planId, executeResult)
	return nil
}

/* Ended by AICoder, pid:r66ea26be852726143a908c7c04a400ebd184bb8 */

/* Started by AICoder, pid:te69bx14d7d96ec14bf20a37906b9a0f7858d58e */
func UpdateStopFinishTimeValues(id string, finishTime time.Time) error {
	if suc := models.UpdateResourceByResultId(constant.TABLE_NAME_InspectionResultTable, id, map[string]interface{}{"finishTime": finishTime}); suc {
		return nil
	}
	logger.Errorf("update db updatef inishtimevalues %s values %v failed", id, finishTime)
	return nil
}

/* Ended by AICoder, pid:te69bx14d7d96ec14bf20a37906b9a0f7858d58e */

/* Started by AICoder, pid:wf6f9nf9da6acee14f260a3110fab80617982774 */
func UpdateStopStartTimeValues(id string, startTime time.Time) error {
	if suc := models.UpdateResourceByResultId(constant.TABLE_NAME_InspectionResultTable, id, map[string]interface{}{"startTime": startTime}); suc {
		return nil
	}
	logger.Errorf("update db update stopstarttimevalues %s values %v failed", id, startTime)
	return nil
}

/* Ended by AICoder, pid:wf6f9nf9da6acee14f260a3110fab80617982774 */

/* Started by AICoder, pid:v1d19fe3bdo1a4314fb20890202cb4171d732aa4 */
func UpdateExecuteTime(id string, finishTime time.Time, startTime time.Time) error {
	errUpdatePlanExecuteEndTime := UpdatePlanExecuteEndTime(id, finishTime)
	if errUpdatePlanExecuteEndTime != nil {
		return errUpdatePlanExecuteEndTime
	}

	errUpdatePlanExecuteStartTime := UpdatePlanExecuteStartTime(id, startTime)
	if errUpdatePlanExecuteStartTime != nil {
		return errUpdatePlanExecuteStartTime
	}

	return nil
}

/* Ended by AICoder, pid:v1d19fe3bdo1a4314fb20890202cb4171d732aa4 */

/* Started by AICoder, pid:xef92v78fckfecd14d3f0961706a191ae8b3f86b */
func UpdateExecuteResult(id string, executeResult string, executePlanPerson string) error {
	errUpdatePlanExecuteResult := UpdatePlanExecuteResult(id, executeResult)
	if errUpdatePlanExecuteResult != nil {
		return errUpdatePlanExecuteResult
	}

	errUpdatePlanExecutePlanPerson := UpdatePlanExecutePlanPerson(id, executePlanPerson)
	if errUpdatePlanExecutePlanPerson != nil {
		return errUpdatePlanExecutePlanPerson
	}

	return nil
}

/* Ended by AICoder, pid:xef92v78fckfecd14d3f0961706a191ae8b3f86b */

func UpdateExecuteStartTimeAndResult(id string, executeResult string, startTime time.Time) error {
	errUpdatePlanExecuteStartTime := UpdatePlanExecuteStartTime(id, startTime)
	if errUpdatePlanExecuteStartTime != nil {
		return errUpdatePlanExecuteStartTime
	}

	errUpdatePlanExecuteResult := UpdatePlanExecuteResult(id, executeResult)
	if errUpdatePlanExecuteResult != nil {
		return errUpdatePlanExecuteResult
	}

	return nil
}

func UpdateExecuteEndTimeAndResult(id string, executeResult string, finishTime time.Time) error {
	errUpdatePlanExecuteEndTime := UpdatePlanExecuteEndTime(id, finishTime)
	if errUpdatePlanExecuteEndTime != nil {
		return errUpdatePlanExecuteEndTime
	}

	errUpdatePlanExecuteResult := UpdatePlanExecuteResult(id, executeResult)
	if errUpdatePlanExecuteResult != nil {
		return errUpdatePlanExecuteResult
	}

	return nil
}

func UpdateExecuteEndTimeAndPlanPerson(id string, executePlanPerson string, finishTime time.Time) error {
	errUpdatePlanExecuteEndTime := UpdatePlanExecuteEndTime(id, finishTime)
	if errUpdatePlanExecuteEndTime != nil {
		return errUpdatePlanExecuteEndTime
	}

	errUpdatePlanExecutePlanPerson := UpdatePlanExecutePlanPerson(id, executePlanPerson)
	if errUpdatePlanExecutePlanPerson != nil {
		return errUpdatePlanExecutePlanPerson
	}

	return nil
}

/* Started by AICoder, pid:ud9e4a528d62b6e1407a0abb60eae023e0b352cc */
func UpdateExecute(id string, executeResult string, executePlanPerson string, finishTime time.Time, startTime time.Time) error {
	errUpdatePlanExecuteEndTime := UpdatePlanExecuteEndTime(id, finishTime)
	if errUpdatePlanExecuteEndTime != nil {
		return errUpdatePlanExecuteEndTime
	}

	errUpdatePlanExecuteStartTime := UpdatePlanExecuteStartTime(id, startTime)
	if errUpdatePlanExecuteStartTime != nil {
		return errUpdatePlanExecuteStartTime
	}

	errUpdatePlanExecuteResult := UpdatePlanExecuteResult(id, executeResult)
	if errUpdatePlanExecuteResult != nil {
		return errUpdatePlanExecuteResult
	}

	errUpdatePlanExecutePlanPerson := UpdatePlanExecutePlanPerson(id, executePlanPerson)
	if errUpdatePlanExecutePlanPerson != nil {
		return errUpdatePlanExecutePlanPerson
	}

	return nil
}

/* Ended by AICoder, pid:ud9e4a528d62b6e1407a0abb60eae023e0b352cc */

/* Started by AICoder, pid:d5c2224bfef4f8214e96093a807b4710e253e13c */
func UpdateStopTime(id string, finishTime time.Time, startTime time.Time) error {
	errUpdateStopFinishTimeValues := UpdateStopFinishTimeValues(id, finishTime)
	if errUpdateStopFinishTimeValues != nil {
		return errUpdateStopFinishTimeValues
	}

	errUpdateStopStartTimeValues := UpdateStopStartTimeValues(id, startTime)
	if errUpdateStopStartTimeValues != nil {
		return errUpdateStopStartTimeValues
	}

	return nil
}

/* Ended by AICoder, pid:d5c2224bfef4f8214e96093a807b4710e253e13c */

/* Started by AICoder, pid:9ae4bw8b7fe17f114fd20a0db000bb1bd6855679 */
func UpdateResultStopStatus(inspectResult map[string]interface{}, id string, status string, executeResult string) error {
	errUpdateStopResultStatus := UpdateStopResultStatus(id, status)
	if errUpdateStopResultStatus != nil {
		return errUpdateStopResultStatus
	}

	if executeResult != "not implemented" {
		errUpdateStopExecuteResultStatus := UpdateStopExecuteResultStatus(id, executeResult)
		if errUpdateStopExecuteResultStatus != nil {
			return errUpdateStopExecuteResultStatus
		}
	}

	return nil
}

/* Ended by AICoder, pid:9ae4bw8b7fe17f114fd20a0db000bb1bd6855679 */

/* Started by AICoder, pid:b4378lab1d637a1140a20932e08a9f1669633e81 */
func UpdateTime(id string, finishTime time.Time, startTime time.Time) error {
	errUpdateFinishTimeValues := UpdateFinishTimeValues(id, finishTime)
	if errUpdateFinishTimeValues != nil {
		return errUpdateFinishTimeValues
	}

	errUpdateStartTimeValues := UpdateStartTimeValues(id, startTime)
	if errUpdateStartTimeValues != nil {
		return errUpdateStartTimeValues
	}

	return nil
}

/* Ended by AICoder, pid:b4378lab1d637a1140a20932e08a9f1669633e81 */

/* Started by AICoder, pid:ra954908cbk281a140740949d0499517e0d672d6 */
func GetPlanResult(inspectionResult map[string]interface{}, planResult *PlanResult) error {
	jsonData, err := json.Marshal(inspectionResult)
	if err != nil {
		logger.Errorf("error inspectionplan marshaling json: %v", err)
		return err
	}

	err = json.Unmarshal(jsonData, &planResult)
	if err != nil {
		logger.Errorf("error planinspect marshaling json: %v", err)
		return err
	}
	return nil
}

/* Ended by AICoder, pid:ra954908cbk281a140740949d0499517e0d672d6 */

/* Started by AICoder, pid:od97dl5d3cx0104140220bd1d083b71ac7865c06 */
func GetResultValues(inspectionResult map[string]interface{}, resultValues *ResultValues) error {
	jsonData, err := json.Marshal(inspectionResult)
	if err != nil {
		logger.Errorf("error inspectionplan marshaling json: %v", err)
		return err
	}

	err = json.Unmarshal(jsonData, &resultValues)
	if err != nil {
		logger.Errorf("error planinspect marshaling json: %v", err)
		return err
	}

	return nil
}

/* Ended by AICoder, pid:od97dl5d3cx0104140220bd1d083b71ac7865c06 */

/* Started by AICoder, pid:49df7784e5j6c21143260b65a0b09b351e22e25a */
func GetPlanResultValues(inspectResults PlanResultValues, inspectionResult []map[string]interface{}) (*[]PlanResult, error) {
	var planResults []PlanResult
	for _, inspectResult := range inspectionResult {

		errGetResultValue := GetResultValue(inspectResult, &inspectResults)
		if errGetResultValue != nil {
			logger.Errorf("error inspectlist planlist marshaling json: %v", errGetResultValue)
			return nil, errGetResultValue
		}

		finishTime := inspectResults.FinishTime.Format(time.RFC3339)

		if finishTime == "0001-01-01T00:00:00Z" {
			finishTime = ""
		}

		planResult := PlanResult{
			Id:                inspectResult["id"].(string),
			StartTime:         inspectResults.StartTime.Format(time.RFC3339),
			EndTime:           finishTime,
			ExecutePlanPerson: inspectResult["executePlanPerson"].(string),
			ExecuteResult:     inspectResult["executeResult"].(string),
			Status:            inspectResult["status"].(string),
			CompletedCount:    inspectResults.CompletedCount,
			RemainingTime:     inspectResults.RemainingTime,
		}
		planResults = append(planResults, planResult)
	}
	return &planResults, nil
}

/* Ended by AICoder, pid:49df7784e5j6c21143260b65a0b09b351e22e25a */

/* Started by AICoder, pid:va7ach997fh1d4c1407109fef041b3592bc158c7 */
func GetPlanResultListFromDb(clusterId string, id string) (interface{}, error) {
	inspectionPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		return nil, err
	}

	if inspectionPlan != nil {
		var inspectResults PlanResultValues
		var inspectPlan InspectPlan

		errGetPlanList := GetPlanValue(inspectionPlan, &inspectPlan)
		if errGetPlanList != nil {
			logger.Errorf("error inspectlist planlist marshaling json: %v", err)
			return nil, errGetPlanList
		}

		nodeList, inspectTaskName, err := GetNodeListStrAndInspectTaskNameStr(inspectPlan)
		if err != nil {
			return nil, err
		}

		condtion := map[string]interface{}{"planId": inspectPlan.Id}
		inspectionResult, err := models.QueryResourceByCondition(constant.TABLE_NAME_InspectionResultTable, condtion)
		if err != nil {
			return nil, err
		}

		planResults, err := GetPlanResultValues(inspectResults, inspectionResult)
		if err != nil {
			logger.Errorf("get plan resultvalues is failed :%v", err)
			return nil, err
		}

		data, err := GetPlanResultList(&inspectPlan, nodeList, inspectTaskName, planResults)
		if err != nil {
			return nil, fmt.Errorf("get plan result list is failed :%v", err)
		}

		return data, nil
	}
	return nil, nil
}

/* Ended by AICoder, pid:va7ach997fh1d4c1407109fef041b3592bc158c7 */

/* Started by AICoder, pid:fea0057ec1g61e61449309d400f380374a92b5b4 */
func GetPlanResultList(inspectPlan *InspectPlan, nodeList []string, inspectTaskName []string, planResults *[]PlanResult) (map[string]interface{}, error) {
	modifyTime, err := GetModifyTime(inspectPlan)
	if err != nil {
		logger.Errorf("getmodifytime is failed :%v", err)
		return nil, err
	}

	plan := PlanResultList{
		Id:                  inspectPlan.Id,
		Name:                inspectPlan.Name,
		ClusterId:           inspectPlan.ClusterId,
		ClusterName:         inspectPlan.ClusterName,
		Scene:               inspectPlan.Scene,
		NodeList:            nodeList,
		InspectTaskName:     inspectTaskName,
		CreateTime:          inspectPlan.CreateTime.Format(time.RFC3339),
		LatestModifyTime:    modifyTime,
		CreateMode:          inspectPlan.CreateMode,
		CreateStrategy:      inspectPlan.CreateStrategy,
		ExecuteStrategyTime: inspectPlan.ExecuteStrategyTime.Format(time.RFC3339),
		DesignPlanPerson:    inspectPlan.DesignPlanPerson,
		ModifyPlanPerson:    inspectPlan.ModifyPlanPerson,
		PlanResult:          *planResults,
	}

	data, err := cwsmutils.Struct2Map(plan)
	if err != nil {
		logger.Errorf("struct to map is failed :%v", err)
		return nil, err
	}
	return data, nil
}

/* Ended by AICoder, pid:fea0057ec1g61e61449309d400f380374a92b5b4 */

/* Started by AICoder, pid:r75eci4fc2y720d1459409c300e9e604d7795def */
func GetModifyTime(inspectPlan *InspectPlan) (string, error) {
	modifyTime := inspectPlan.LatestModifyTime.Format(time.RFC3339)

	if modifyTime == "0001-01-01T00:00:00Z" {
		modifyTime = ""
	}
	return modifyTime, nil
}

/* Ended by AICoder, pid:r75eci4fc2y720d1459409c300e9e604d7795def */

/* Started by AICoder, pid:yf1f76871895d8314001097280685819ae52ce89 */
func GetnodeListAndinspectTaskNameValue(inspectionPlan map[string]interface{}) ([]string, []string, error) {
	nodeList, err := cwsmutils.StringToSlice(inspectionPlan["nodeList"].(string))
	if err != nil {
		return nil, nil, err
	}

	inspectTaskName, err := cwsmutils.StringToSlice(inspectionPlan["inspectTaskName"].(string))
	if err != nil {
		return nil, nil, err
	}
	return nodeList, inspectTaskName, nil
}

/* Ended by AICoder, pid:yf1f76871895d8314001097280685819ae52ce89 */

func GetPlanResultFromDb(clusterId string, id string, resultId string) (interface{}, error) {
	inspectionPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		return nil, err
	}

	if inspectionPlan != nil {

		inspectionResult, err := models.QueryResourceById(constant.TABLE_NAME_InspectionResultTable, resultId)
		if err != nil {
			return nil, err
		}

		var inspectResult PlanResultValues

		errGetResultValue := GetResultValue(inspectionResult, &inspectResult)
		if errGetResultValue != nil {
			logger.Errorf("error inspectlist planlist marshaling json: %v", err)
			return nil, errGetResultValue
		}

		data, err := GetInspectionPlanResult(&inspectResult, inspectionPlan, resultId)
		if err != nil {
			return nil, err
		}

		return data, nil
	}
	return nil, nil
}

func GetInspectionPlanResult(inspectResult *PlanResultValues, inspectionPlan map[string]interface{}, resultId string) (map[string]interface{}, error) {
	nodeList, inspectTaskName, err := GetnodeListAndinspectTaskNameValue(inspectionPlan)
	if err != nil {
		return nil, err
	}

	finishTime := inspectResult.FinishTime.Format(time.RFC3339)

	if finishTime == "0001-01-01T00:00:00Z" {
		finishTime = ""
	}

	if inspectResult.InspectionResult == "" {
		data, err := GetInspectionPlanResultNil(inspectionPlan, finishTime, nodeList, inspectTaskName, inspectResult, resultId)
		if err != nil {
			return nil, err
		}

		return data, nil
	} else {
		data, err := GetInspectionPlanResultNotNil(inspectionPlan, finishTime, nodeList, inspectTaskName, inspectResult, resultId)
		if err != nil {
			return nil, err
		}
		return data, nil
	}

}

/* Started by AICoder, pid:o6891y908fd9f111438509c220143e2a90a61a11 */
func GetInspectionPlanResultNil(inspectionPlan map[string]interface{}, finishTime string, nodeList []string, inspectTaskName []string, inspectResult *PlanResultValues, resultId string) (map[string]interface{}, error) {
	planResult := InspectionPlanResult{
		Id:                resultId,
		PlanId:            inspectionPlan["id"].(string),
		PlanName:          inspectionPlan["name"].(string),
		ClusterName:       inspectionPlan["clusterName"].(string),
		ClusterId:         inspectionPlan["clusterId"].(string),
		Scene:             inspectionPlan["scene"].(string),
		NodeList:          nodeList,
		InspectTaskName:   inspectTaskName,
		StartTime:         inspectResult.StartTime.Format(time.RFC3339),
		EndTime:           finishTime,
		ExecutePlanPerson: inspectResult.ExecutePlanPerson,
		Status:            inspectResult.Status,
		ExecuteResult:     inspectResult.ExecuteResult,
		CompletedCount:    inspectResult.CompletedCount,
		RemainingTime:     inspectResult.RemainingTime,
		InspectionResult:  nil,
	}

	data, err := cwsmutils.Struct2Map(planResult)
	if err != nil {
		return nil, fmt.Errorf("inspect plan result is failed:%v", err)
	}
	return data, nil
}

/* Ended by AICoder, pid:o6891y908fd9f111438509c220143e2a90a61a11 */

/* Started by AICoder, pid:ub7eb4d83dk92b5145c50ad260ee62288a77a6c9 */
func GetInspectionPlanResultNotNil(inspectionPlan map[string]interface{}, finishTime string, nodeList []string, inspectTaskName []string, inspectResult *PlanResultValues, resultId string) (map[string]interface{}, error) {
	planResult := InspectionPlanResult{
		Id:                resultId,
		PlanId:            inspectionPlan["id"].(string),
		PlanName:          inspectionPlan["name"].(string),
		ClusterName:       inspectionPlan["clusterName"].(string),
		ClusterId:         inspectionPlan["clusterId"].(string),
		Scene:             inspectionPlan["scene"].(string),
		NodeList:          nodeList,
		InspectTaskName:   inspectTaskName,
		StartTime:         inspectResult.StartTime.Format(time.RFC3339),
		EndTime:           finishTime,
		ExecutePlanPerson: inspectResult.ExecutePlanPerson,
		Status:            inspectResult.Status,
		ExecuteResult:     inspectResult.ExecuteResult,
		CompletedCount:    inspectResult.CompletedCount,
		RemainingTime:     inspectResult.RemainingTime,
		InspectionResult:  json.RawMessage(inspectResult.InspectionResult),
	}

	data, err := cwsmutils.Struct2Map(planResult)
	if err != nil {
		logger.Errorf("inspect plan result is failed:%v", err)
		return nil, err
	}
	return data, nil
}

/* Ended by AICoder, pid:ub7eb4d83dk92b5145c50ad260ee62288a77a6c9 */

func StopInspect(clusterId string, id string, author string) error {
	_, stopInspectErr, suc := wsm.InspectHandler(clusterId, nil, "stop", id)
	if !suc {
		if stopInspectErr == "" {
			return fmt.Errorf("an internal error occurred in the code")
		} else {
			return fmt.Errorf(stopInspectErr)
		}
	}

	inspectionPlan, err := models.QueryResourceById(constant.TABLE_NAME_InspectionPlanTable, id)
	if err != nil {
		logger.Errorf("query resource by id failed: %v", err)
		return err
	}

	errUpdateStopStatus := UpdateStopStatus(id, inspectionPlan)
	if errUpdateStopStatus != nil {
		return errUpdateStopStatus
	}

	return nil
}

/* Started by AICoder, pid:fb0c3ff8c6333d8146380b6e508843247976ff7a */
func UpdateStopStatus(id string, inspectionPlan map[string]interface{}) error {
	status := "stopped"
	errUpdatePlanStatus := UpdatePlanStatus(id, status)
	if errUpdatePlanStatus != nil {
		return errUpdatePlanStatus
	}
	errUpdateResultStatus := UpdateResultStatus(inspectionPlan["planResultId"].(string), status)
	if errUpdateResultStatus != nil {
		return errUpdateResultStatus
	}

	executeResult := "manually stopped"
	errUpdateExecuteResultStatus := UpdateExecuteResultStatus(inspectionPlan["planResultId"].(string), executeResult)
	if errUpdateExecuteResultStatus != nil {
		return errUpdateExecuteResultStatus
	}

	currentResultTime := time.Now()
	finishTime := currentResultTime

	errUpdateFinishTimeValues := UpdateFinishTimeValues(inspectionPlan["planResultId"].(string), finishTime)
	if errUpdateFinishTimeValues != nil {
		return errUpdateFinishTimeValues
	}
	return nil
}

/* Ended by AICoder, pid:fb0c3ff8c6333d8146380b6e508843247976ff7a */

func DeleteInspect(clusterId string, reqBody DeleteInspectReq, author string) error {
	res, err := cwsmutils.Struct2Map(reqBody)
	if err != nil {
		return err
	}

	_, _, suc := wsm.InspectHandler(clusterId, res, "delete", "")
	if !suc {
		return fmt.Errorf("delete inspect plan failed")
	}

	for _, id := range reqBody.Id {

		logger.Info(" id:%v", id)

		if !models.DeleteResourceById(constant.TABLE_NAME_InspectionPlanTable, id) {
			return errors.New("db delete inspection result table failed for id:%s " + id)
		}
	}

	return nil
}

func DeleteInspectResult(clusterId string, reqBody DeleteInspectResultReq, planId string) error {
	res, err := cwsmutils.Struct2Map(reqBody)
	if err != nil {
		return err
	}

	_, _, suc := wsm.InspectHandler(clusterId, res, "deleteInspect", "")
	if !suc {
		return fmt.Errorf("delete inspect result failed")
	}

	var resultIds []string

	for _, id := range reqBody.Id {
		logger.Info(" id:%v", id)
		if !models.DeleteResourceById(constant.TABLE_NAME_InspectionResultTable, id) {
			return errors.New(fmt.Sprintf("db delete inspection result table failed for id: %s", id))
		}
	}

	condtion := map[string]interface{}{"planId": planId}
	inspectionResults, err := models.QueryResourceByCondition(constant.TABLE_NAME_InspectionResultTable, condtion)
	if err != nil {
		return err
	}

	for _, inspectionResult := range inspectionResults {
		if inspectionResult != nil {
			planResultId, ok := inspectionResult["id"].(string)
			if !ok {
				return errors.New(fmt.Sprintf("get result id from inspection result table is failed for result: %v", inspectionResult))
			}
			resultIds = append(resultIds, planResultId)
		}
	}

	var maxResultId string
	if len(resultIds) > 0 {
		maxResultId = resultIds[0]
		for _, resultId := range resultIds[1:] {
			if resultId > maxResultId {
				maxResultId = resultId
			}
		}

		errUpdatePlanResultId := UpdatePlanResultIdByMaxResultIds(planId, maxResultId)
		if errUpdatePlanResultId != nil {
			return errUpdatePlanResultId
		}

	} else {
		logger.Warn("No valid result ids found.")
	}

	return nil
}

/* Started by AICoder, pid:r3fc91e39099bf41413a08a7d0b4610cc438eaf7 */
func UpdatePlanResultIdByMaxResultIds(planId string, maxResultId string) error {
	errUpdateResultId := UpdateResultId(planId, maxResultId)
	if errUpdateResultId != nil {
		return errUpdateResultId
	}

	return nil
}

/* Ended by AICoder, pid:r3fc91e39099bf41413a08a7d0b4610cc438eaf7 */

func UpdateRunningStatusToDisconnected(resultId string) error {
	logger.Info("start UpdateRunningStatusToDisconnected")
	inspectionResult, err := models.QueryResourceById(constant.TABLE_NAME_InspectionResultTable, resultId)
	if err != nil {
		return fmt.Errorf("failed to query inspection result: %v", err)
	}

	logger.Info("UpdateRunningStatusToDisconnected inspectionResult:%v", inspectionResult)

	if inspectionResult == nil {
		return fmt.Errorf("inspection result not found")
	}

	logger.Info("inspectionResult is not nil")

	var inspectionResultMap map[string]interface{}
	if err := json.Unmarshal([]byte(inspectionResult["inspectionResult"].(string)), &inspectionResultMap); err != nil {
		return fmt.Errorf("failed to unmarshal inspection result: %v", err)
	}

	logger.Info("json.Unmarshal inspectionResultMap:%v", inspectionResultMap)

	resultFields := []string{"healthyCheckResult", "rdmaResult", "gpuCheckResult", "cclResult", "modelResult"}
	for _, field := range resultFields {
		if result, ok := inspectionResultMap[field].(map[string]interface{}); ok {
			if status, ok := result["status"].(string); ok && status == "running" {
				result["status"] = "disconnected"
				break
			}
		}
	}

	logger.Info("resultFieldsresultFieldsresultFields")

	updatedResult, err := json.Marshal(inspectionResultMap)
	if err != nil {
		return fmt.Errorf("failed to marshal updated inspection result: %v", err)
	}

	logger.Info("UpdateRunningStatusToDisconnected updatedResult:%v", updatedResult)

	inspectionResult["inspectionResult"] = string(updatedResult)
	if suc := models.UpdateResourceById(constant.TABLE_NAME_InspectionResultTable, resultId, inspectionResult); !suc {
		logger.Errorf("failed to update inspection result in database for id: %s", resultId)
		return nil
	}

	logger.Info("UpdateResource success")

	return nil
}
