package controllers

import (
	"cwsm/infra/cwsmutils"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"reflect"

	gomonkey "github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:u6a16yadbd672e81480909ffa095197095890a8f */
var _ = Describe("AcceleratorController Get", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *AcceleratorController
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &AcceleratorController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetAccelerator returns error", func() {
		It("should respond with error message and status 400", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/accelerator/cluster/%s", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("get gpu info failed")

			patcher.ApplyFunc(GetAccelerator, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			var apiResponseStatus int
			var apiResponseData interface{}
			patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse",
				func(c *cwsmutils.Controller, status int, data interface{}) {
					apiResponseStatus = status
					apiResponseData = data
				})

			ctrl.Get()

			Expect(apiResponseStatus).To(Equal(http.StatusBadRequest))
			Expect(apiResponseData).To(Equal(map[string]string{"message": "get gpu info failed"}))
		})
	})

	Context("When GetAccelerator returns success", func() {
		It("should respond with data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/accelerator/cluster/%s", clusterId), nil)
			ctrl.Ctx.Request = req
			mockData := map[string]interface{}{"gpu": "info"}

			patcher.ApplyFunc(GetAccelerator, func(clusterId string) (interface{}, error) {
				return mockData, nil
			})

			var apiResponseStatus int
			var apiResponseData interface{}
			patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse",
				func(c *cwsmutils.Controller, status int, data interface{}) {
					apiResponseStatus = status
					apiResponseData = data
				})

			ctrl.Get()

			Expect(apiResponseStatus).To(Equal(http.StatusOK))
			Expect(apiResponseData).To(Equal(mockData))
		})
	})
})

/* Ended by AICoder, pid:u6a16yadbd672e81480909ffa095197095890a8f */

/* Started by AICoder, pid:016c61753ed460b142040a3b3085c97da689acbf */
var _ = Describe("AcceleratorController GetNodes", func() {
	var (
		patcher        *gomonkey.Patches
		ctrl           *AcceleratorController
		cwsmController *cwsmutils.Controller
	)

	BeforeEach(func() {
		patcher = gomonkey.NewPatches()
		patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse", func(*cwsmutils.Controller, int, interface{}) {
			return
		})

		beegoController := GetBeegoController()

		cwsmController = &cwsmutils.Controller{
			Controller: *beegoController,
		}
		ctrl = &AcceleratorController{
			Controller: *cwsmController,
		}
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When GetNodes returns error", func() {
		It("should respond with error message and status 400", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/accelerator/cluster/%s/nodes", clusterId), nil)
			ctrl.Ctx.Request = req
			err := errors.New("get node info failed")

			patcher.ApplyFunc(GetNodes, func(clusterId string) (interface{}, error) {
				return nil, err
			})

			var apiResponseStatus int
			var apiResponseData interface{}
			patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse",
				func(c *cwsmutils.Controller, status int, data interface{}) {
					apiResponseStatus = status
					apiResponseData = data
				})

			ctrl.GetNodes()

			Expect(apiResponseStatus).To(Equal(http.StatusBadRequest))
			Expect(apiResponseData).To(Equal(map[string]string{"message": "get node info failed"}))
		})
	})

	Context("When GetNodes returns success", func() {
		It("should respond with data and status 200", func() {
			clusterId := "cluster123"
			req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1.0/accelerator/cluster/%s/nodes", clusterId), nil)
			ctrl.Ctx.Request = req
			mockData := map[string]interface{}{"nodes": "info"}

			patcher.ApplyFunc(GetNodes, func(clusterId string) (interface{}, error) {
				return mockData, nil
			})

			var apiResponseStatus int
			var apiResponseData interface{}
			patcher.ApplyMethod(reflect.TypeOf(cwsmController), "ApiResponse",
				func(c *cwsmutils.Controller, status int, data interface{}) {
					apiResponseStatus = status
					apiResponseData = data
				})

			ctrl.GetNodes()

			Expect(apiResponseStatus).To(Equal(http.StatusOK))
			Expect(apiResponseData).To(Equal(mockData))
		})
	})
})

/* Ended by AICoder, pid:016c61753ed460b142040a3b3085c97da689acbf */
