//inspectiontask

package controllers

import (
	"cwsm/infra/cwsmutils"
	"net/http"
)

type InspectiontaskController struct {
	cwsmutils.Controller
}

func (e *InspectiontaskController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetInspectiontask(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *InspectiontaskController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := ActivateInspectiontask(clusterId, e.Ctx.Input.RequestBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *InspectiontaskController) Delete() {
	reqParam := e.getRequestParam("delete")
	err := DeleteInspectiontask(reqParam)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func (e *InspectiontaskController) getRequestParam(method string) InspectiontaskRequestParam {
	clusterId := e.Ctx.Input.Param(":clusterId")
	return InspectiontaskRequestParam{ClusterId: clusterId}
}
func (e *InspectiontaskController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
