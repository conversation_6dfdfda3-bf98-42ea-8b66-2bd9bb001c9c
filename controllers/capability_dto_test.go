package controllers

import (
	"cwsm/infra/wsm"
	"fmt"

	gomonkey "github.com/agiledragon/gomonkey/v2"

	. "github.com/onsi/ginkgo"

	. "github.com/onsi/gomega"
)

/* Started by AICoder, pid:rfe94j3d3cpb5f214d7e08d2b0e2ac511ab69dce */
var _ = Describe("TestGetAccelerator", func() {
	var (
		clusterId string
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.AdrmHandler returns an error", func() {
		It("should return the error", func() {
			patcher.ApplyFunc(wsm.AdrmHandler, func(clusterId string, condition interface{}, method string) (map[string]interface{}, bool) {
				return nil, false
			})

			result, err := GetAccelerator(clusterId)

			Expect(err).To(Equal(fmt.<PERSON>rrorf("get information from acceleratordevice failed")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.AdrmHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "name": "gpu1"}
			patcher.ApplyFunc(wsm.AdrmHandler, func(clusterId string, condition interface{}, method string) (map[string]interface{}, bool) {
				return data, true
			})

			result, err := GetAccelerator(clusterId)

			Expect(err).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.AdrmHandler is called with correct parameters", func() {
		It("should pass the right method parameter", func() {
			var passedMethod string
			patcher.ApplyFunc(wsm.AdrmHandler, func(cId string, condition interface{}, method string) (map[string]interface{}, bool) {
				passedMethod = method
				return nil, true
			})

			_, _ = GetAccelerator(clusterId)

			Expect(passedMethod).To(Equal("get"))
		})
	})
})

/* Ended by AICoder, pid:rfe94j3d3cpb5f214d7e08d2b0e2ac511ab69dce */

/* Started by AICoder, pid:63a9ara7ee8d371149fc0a104025025e7db61b9f */
var _ = Describe("TestGetNodes", func() {
	var (
		clusterId string
		patcher   *gomonkey.Patches
	)

	BeforeEach(func() {
		clusterId = "cluster123"
		patcher = gomonkey.NewPatches()
	})

	AfterEach(func() {
		patcher.Reset()
	})

	Context("When wsm.AdrmHandler returns an error", func() {
		It("should return the error", func() {
			patcher.ApplyFunc(wsm.AdrmHandler, func(clusterId string, condition interface{}, method string) (map[string]interface{}, bool) {
				return nil, false
			})

			result, err := GetNodes(clusterId)

			Expect(err).To(Equal(fmt.Errorf("get node information failed")))
			Expect(result).To(BeNil())
		})
	})

	Context("When wsm.AdrmHandler returns data correctly", func() {
		It("should return the data and no error", func() {
			data := map[string]interface{}{"id": 1, "name": "node1"}
			patcher.ApplyFunc(wsm.AdrmHandler, func(clusterId string, condition interface{}, method string) (map[string]interface{}, bool) {
				return data, true
			})

			result, err := GetNodes(clusterId)

			Expect(err).To(BeNil())
			Expect(result).To(Equal(data))
		})
	})

	Context("When wsm.AdrmHandler is called with correct parameters", func() {
		It("should pass the right method parameter", func() {
			var passedMethod string
			patcher.ApplyFunc(wsm.AdrmHandler, func(cId string, condition interface{}, method string) (map[string]interface{}, bool) {
				passedMethod = method
				return nil, true
			})

			_, _ = GetNodes(clusterId)

			Expect(passedMethod).To(Equal("getnode"))
		})
	})
})

/* Ended by AICoder, pid:63a9ara7ee8d371149fc0a104025025e7db61b9f */
