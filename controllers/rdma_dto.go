//rdma
package controllers

import (
	"cwsm/infra/wsm"
	"fmt"
)

/* Started by AICoder, pid:b6e524f102ad490395b5bee773076361 */
func GetRdma(clusterId string) (interface{}, error) {
	keywords := wsm.RdmaWsmKeywords{
		ClusterId: clusterId,
	}

	rdma, err, suc := wsm.Rdma<PERSON>andler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}

	return rdma, nil
}

/* Ended by AICoder, pid:b6e524f102ad490395b5bee773076361 */

/* Started by AICoder, pid:653a9242dce14bb69078caaaa4401091 */
func GetRdmaTopo(clusterId string) (interface{}, error) {
	keywords := wsm.RdmaWsmKeywords{
		ClusterId: clusterId,
	}

	rdma, err, suc := wsm.<PERSON>ma<PERSON><PERSON><PERSON>(keywords, nil, "getrdmatopo")
	if !suc {
		if err == "" {
			return nil, fmt.<PERSON><PERSON><PERSON>("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}

	return rdma, nil
}

/* Ended by AICoder, pid:653a9242dce14bb69078caaaa4401091 */

/* Started by AICoder, pid:3001bcc6417941b882db0e830e38d715 */
func ActivateRdma(clusterId string, reqBody []byte) (interface{}, error) {
	keywords := wsm.RdmaWsmKeywords{
		ClusterId: clusterId,
	}

	rdma, err, suc := wsm.RdmaHandler(keywords, reqBody, "post")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("an internal error occurred in the code")
		} else {
			return nil, fmt.Errorf(err)
		}
	}

	return rdma, nil
}

/* Ended by AICoder, pid:3001bcc6417941b882db0e830e38d715 */

/* Started by AICoder, pid:9b0f5d9cafa64d6287c94182b7b19752 */
func StopRdma(clusterId string) error {
	keywords := wsm.RdmaWsmKeywords{
		ClusterId: clusterId,
	}

	_, err, suc := wsm.RdmaHandler(keywords, nil, "stop")
	if !suc {
		if err == "" {
			return fmt.Errorf("an internal error occurred in the code")
		} else {
			return fmt.Errorf(err)
		}
	}
	return nil
}

/* Ended by AICoder, pid:9b0f5d9cafa64d6287c94182b7b19752 */
