package controllers

import (
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"net/http"
)

//inspect POST
type InspectController struct {
	cwsmutils.Controller
}

/* Started by AICoder, pid:l9398o3bb4p5b8814d8d0b40706c2f020706644b */
func (e *InspectController) Get() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	data, err := GetInspect(clusterId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:l9398o3bb4p5b8814d8d0b40706c2f020706644b */

func (e *InspectController) GetOne() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	data, err := GetOneInspect(clusterId, id)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *InspectController) GetPlanResultList() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	data, err := GetPlanResultListFromDb(clusterId, id)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

func (e *InspectController) GetPlanResult() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	resultId := e.Ctx.Input.Param(":resultid")
	data, err := GetPlanResultFromDb(clusterId, id, resultId)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Started by AICoder, pid:x0746023a1racfc1441f0b068084ef190151caaf */
func (e *InspectController) Post() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	var reqBody CreateInspectReq
	if !e.getRequestBody("post", &reqBody) {
		logger.Errorf("Post json.Unmarshal error")
		return
	}

	data, err := CreateInspect(clusterId, reqBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:x0746023a1racfc1441f0b068084ef190151caaf */

/* Started by AICoder, pid:w697aa2240b856b14def0aa540e0bf175b128ce0 */
func (e *InspectController) Modify() {
	var reqBody CreateInspectReq
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	if !e.getRequestBody("post", &reqBody) {
		logger.Errorf("Modify json.Unmarshal error")
		return
	}

	data, err := ModifyInspect(clusterId, id, reqBody)
	e.actionResponse(err, http.StatusOK, data, http.StatusBadRequest)
}

/* Ended by AICoder, pid:w697aa2240b856b14def0aa540e0bf175b128ce0 */

func (e *InspectController) Activate() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	author := e.Ctx.Input.Query("author")
	err := ActivateInspect(clusterId, id, author)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func (e *InspectController) Stop() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	id := e.Ctx.Input.Param(":id")
	author := e.Ctx.Input.Query("author")
	err := StopInspect(clusterId, id, author)
	e.actionResponse(err, http.StatusOK, nil, http.StatusBadRequest)
}

func (e *InspectController) Delete() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	logger.Info("Delete clusterId:%s", clusterId)
	var reqBody DeleteInspectReq
	if !e.getRequestBody("delete", &reqBody) {
		return
	}
	author := e.Ctx.Input.Query("author")
	err := DeleteInspect(clusterId, reqBody, author)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func (e *InspectController) DeleteResult() {
	clusterId := e.Ctx.Input.Param(":clusterId")
	planId := e.Ctx.Input.Param(":planid")
	var reqBody DeleteInspectResultReq
	if !e.getRequestBody("deleteResult", &reqBody) {
		return
	}
	err := DeleteInspectResult(clusterId, reqBody, planId)
	e.actionResponse(err, http.StatusNoContent, nil, http.StatusBadRequest)
}

func (e *InspectController) getRequestBody(method string, reqBody interface{}) bool {
	var err error
	switch method {
	case "post":
		err = json.Unmarshal(e.Ctx.Input.RequestBody, &reqBody)
	case "delete", "deleteResult":
		err = json.Unmarshal(e.Ctx.Input.RequestBody, &reqBody)
	default:
		return false
	}
	if err != nil {
		logger.Errorf("method:%s request body Unmarshal failed", method)
		e.ApiResponse(http.StatusBadRequest, map[string]string{"message": err.Error()})
		return false
	}
	return true
}

func (e *InspectController) actionResponse(err error, sucCodeStatus int, data interface{}, failCodeStatus int) {
	if err != nil {
		e.ApiResponse(failCodeStatus, map[string]string{"message": err.Error()})
		return
	}
	e.ApiResponse(sucCodeStatus, data)
}
