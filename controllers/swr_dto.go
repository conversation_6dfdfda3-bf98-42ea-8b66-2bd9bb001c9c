package controllers

import (
	"cwsm/infra/wsm"
	"fmt"
)

//swr

/* Started by AICoder, pid:b6e524f102ad490395b5bee773076361 */
func GetSwr(clusterId string) (interface{}, error) {
	keywords := wsm.SwrWsmKeywords{
		ClusterId: clusterId,
	}

	swr, err, suc := wsm.SwrHandler(keywords, nil, "get")
	if !suc {
		if err == "" {
			return nil, fmt.Errorf("failed to get cluster information or timeout")
		} else {
			return nil, fmt.Erro<PERSON>(err)
		}
	}

	return swr, nil
}

/* Ended by AICoder, pid:b6e524f102ad490395b5bee773076361 */
