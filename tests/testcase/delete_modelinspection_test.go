package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"errors"
	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func deleteModelinspectionEvaSubscription(id string) *httptest.ResponseRecorder {
	request, err := http.NewRequest("DELETE", constant.CwsmPrefix+"/cluster/cluster1/apts/modelinspection/delete/"+id, nil)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for deleting evaluate by modelinspection", func() {
	var fp DeleteModelinspectionPatches
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	It("delete modelinspection, Return 400 When Wsm delete modelinspection Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
			int, error) {
			return nil, make(map[string][]string), http.StatusUnauthorized, nil
		}))

		response := deleteModelinspectionEvaSubscription("evalu-uuid1")
		Expect(response.Code).To(Equal(http.StatusBadRequest))
	})

	/* Started by AICoder, pid:c62fbb271e2c46a1841e5341273c1781 */
	It("delete modelinspection, Return 400 When Wsm delete modelinspection Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
			int, error) {
			return nil, nil, 0, errors.New(`{"err":"delete modelinspection info error"}`)
		}))

		response := deleteInspectiontaskEvaSubscription()
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"delete modelinspection info error"}`))
	})
	/* Ended by AICoder, pid:c62fbb271e2c46a1841e5341273c1781 */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
