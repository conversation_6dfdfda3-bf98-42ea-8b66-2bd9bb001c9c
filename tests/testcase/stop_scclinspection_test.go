package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func stopScclinspectionEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/scclinspection/stop", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for stopping by scclinspection", func() {
	var fp StopScclinspectionPatches
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	It("stop scclinspection, Return 400 because unmarshaling failed", func() {
		response := scclinspectionEvaSubscription(`{"aaa":111"}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
	})

	It("stop scclinspection, Return 400 When Wsm stop scclinspection Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusForbidden, nil
		}))

		response := stopScclinspectionEvaSubscription(`{"aaa":111"}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
	})

	AfterEach(func() {
		fp.CleanPatches()
	})
})
