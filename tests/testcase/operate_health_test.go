package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/tools/commontools/restful"
	"errors"

	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func healthEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/healthcheck", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for activate health", func() {
	var fp ActivateHealthPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("activate health", func() {
		It("activate health, Return 200 When activate Succeed", func() {
			response := healthEvaSubscription(`{"object": "cluster","objectList":["test1", "test2"],"checkSubInfo":{"gpuConsistencyCheck":"gpuharddropnum"},"healthCheckCfg":{"gpuNums":80,"rdmaNums":80}}`)
			Expect(response.Code).To(Equal(http.StatusOK))
		})
	})

	/* Started by AICoder, pid:6c208041f7e34d9b93d2182540bb98ed */
	It("activate health post to wsm fail", func() {
		fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
			return nil, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, errors.New(`{"err":"activate health info error"}`)
		}))
		response := healthEvaSubscription(`{"object": "cluster","objectList":["test1", "test2"],"checkSubInfo":{"gpuConsistencyCheck":"gpuharddropnum"},"healthCheckCfg":{"gpuNums":80,"rdmaNums":80}}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"activate health info error"}`))
	})

	AfterEach(func() {
		fp.CleanPatches()
	})

	Context("activate health failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.PostMethod, func(url string, headers map[string]string, body []byte, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusOK, nil
			}))
		})

		It("activate health fails due to JSON unmarshal error", func() {
			response := healthEvaSubscription(`{"object": "cluster","objectList":["test1", "test2"],"checkSubInfo":{"gpuConsistencyCheck":"gpuharddropnum"},"healthCheckCfg":{"gpuNums":80,"rdmaNums":80}}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:6c208041f7e34d9b93d2182540bb98ed */
})
