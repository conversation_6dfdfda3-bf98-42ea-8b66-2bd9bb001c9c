package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"errors"
	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func deleteInspectiontaskEvaSubscription() *httptest.ResponseRecorder {
	request, err := http.NewRequest("DELETE", constant.CwsmPrefix+"/cluster/cluster1/apts/inspectiontask", nil)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for deleting evaluate by evaluateId", func() {
	var fp DeleteEvaluatePatches
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	It("delete inspectiontask, Return 400 When Wsm delete Inspectiontask Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
			int, error) {
			return nil, make(map[string][]string), http.StatusUnauthorized, nil
		}))

		response := deleteInspectiontaskEvaSubscription()
		Expect(response.Code).To(Equal(http.StatusBadRequest))
	})

	/* Started by AICoder, pid:45715778ad22440489c844d9300ad55b */
	It("delete inspectiontask, Return 400 When Wsm delete Inspectiontask Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
			int, error) {
			return nil, nil, 0, errors.New(`{"err":"delete inspectiontask info error"}`)
		}))

		response := deleteInspectiontaskEvaSubscription()
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"delete inspectiontask info error"}`))
	})
	/* Ended by AICoder, pid:45715778ad22440489c844d9300ad55b */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
