package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"errors"
	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func stopModelinspectionEvaSubscription(id string) *httptest.ResponseRecorder {
	request, err := http.NewRequest("DELETE", constant.CwsmPrefix+"/cluster/cluster1/apts/modelinspection/stop/"+id, nil)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for stopping by modelinspection", func() {
	var fp StopModelinspectionPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	It("stop modelinspection, Return 204 When stop modelinspection Succeed", func() {
		response := stopModelinspectionEvaSubscription("evalu-uuid1")
		Expect(response.Code).To(Equal(http.StatusNoContent))
	})

	It("stop modelinspection, Return 400 When Wsm stop modelinspection Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
			int, error) {
			return nil, make(map[string][]string), http.StatusUnauthorized, nil
		}))

		response := stopModelinspectionEvaSubscription("evalu-uuid1")
		Expect(response.Code).To(Equal(http.StatusBadRequest))
	})

	/* Started by AICoder, pid:c430a431a2a547d8a3cc7acbe5d67651 */
	It("stop modelinspection, Return 400 When Wsm stop modelinspection Build Restful Req Failed", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.DeleteToWsm, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string,
			int, error) {
			return nil, nil, 0, errors.New(`{"err":"stop modelinspection info error"}`)
		}))

		response := stopModelinspectionEvaSubscription("evalu-uuid1")
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"stop modelinspection info error"}`))
	})
	/* Ended by AICoder, pid:c430a431a2a547d8a3cc7acbe5d67651 */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
