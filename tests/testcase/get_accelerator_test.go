package cwsmtest

import (
	"cwsm/infra/constant"
	"encoding/json"
	"net/http"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get accelerator devices", func() {
	var fp OperateEvaluatePatches
	getUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/acceleratordevices"
	getUrlNodes := constant.CwsmPrefix + "/cluster/cluster1/apts/gpunodes"
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("GET accelerator devices success", func() {
		It("GET accelerator devices info success", func() {
			res := httpTestMethod("GET", getUrl, "")
			var result map[string]interface{}
			json.Unmarshal(res.Body.Bytes(), &result)
			Expect(res.Code).To(Equal(http.StatusOK))
			Expect(len(result["items"].([]interface{}))).To(Equal(1))
		})
		It("GET gpu nodes info success", func() {
			res := httpTestMethod("GET", getUrlNodes, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	AfterEach(func() {
		fp.CleanPatches()
	})
})
