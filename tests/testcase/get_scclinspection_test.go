package cwsmtest

import (
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"errors"
	"net/http"
	"reflect"

	"cwsm/tools/commontools/db/dbutil"
	dbcore "cwsm/tools/commontools/db/dbutil/db-core"
	"cwsm/tools/commontools/restful"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = Describe("test for get scclinspection", func() {
	var fp GetScclinspectionPatches
	getUrl := constant.CwsmPrefix + "/cluster/cluster1/apts/scclinspection"
	BeforeEach(func() {
		fp.NewEvaluateFakePatches()
	})

	Context("GET scclinspection success", func() {
		It("GET scclinspection success", func() {
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		AfterEach(func() {
			fp.CleanPatches()
		})
	})

	Context("GET scclinspection failed because of parameter error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyMethod(reflect.TypeOf(fp.pgclient), "Query", func(_ *dbutil.DBClient, table string, condition *dbcore.Condition) ([]map[string]interface{}, error) {
				return []map[string]interface{}{{"cluster": "cluster1"}}, nil
			}))
		})
		It("GET scclinspection success", func() {
			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusOK))
		})
		It("GET scclinspection failed because url has different cluster id", func() {
			url := constant.CwsmPrefix + "/cluster/cluster1/project/project1/apts/evaluate?evaluateId=testid"
			res := httpTestMethod("GET", url, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"db result error accessing clusterid type"}`))
		})

		It("GET scclinspection failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return []byte{}, map[string][]string{"X-Subject-Token": {"a"}}, http.StatusOK, nil
			}))

			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		/* Started by AICoder, pid:373323d8e5c448708caadb2ea096d8a1 */
		It("GET scclinspection failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(restful.GetMethod, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get scclinspection info error"}`)
			}))

			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
		})

		It("GET scclinspection failed because of get to wsm error", func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.GetFromWsmToGetStatus, func(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
				return nil, nil, 0, errors.New(`{"err":"get scclinspection info error"}`)
			}))

			res := httpTestMethod("GET", getUrl, "")
			Expect(res.Code).To(Equal(http.StatusBadRequest))
			Expect(res.Body.String()).To(MatchJSON(`{"message":"get scclinspection info error"}`))
		})
		/* Ended by AICoder, pid:373323d8e5c448708caadb2ea096d8a1 */

		AfterEach(func() {
			fp.CleanPatches()
		})
	})

})
