package cwsmtest

import (
	"testing"

	"cwsm/tools/commontools/security"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/beego/beego/v2/server/web/context"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var handleXSSAttackRspPatch *gomonkey.Patches

func TestCwsmService(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Cwsm Suite")
}

var _ = BeforeSuite(func() {
	handleXSSAttackRspPatch = gomonkey.ApplyFunc(security.HandleXSSAttackRSP, func(ctx *context.Context) {})
})

var _ = AfterSuite(func() {
	handleXSSAttackRspPatch.Reset()
})
