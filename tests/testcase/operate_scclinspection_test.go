package cwsmtest

import (
	"bytes"
	"cwsm/infra/constant"
	"cwsm/infra/wsm"
	"cwsm/tools/commontools/restful"
	"errors"

	"net/http"
	"net/http/httptest"

	"github.com/agiledragon/gomonkey/v2"
	beego "github.com/beego/beego/v2/server/web"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

func scclinspectionEvaSubscription(reqBody string) *httptest.ResponseRecorder {
	body := bytes.NewReader([]byte(reqBody))
	request, err := http.NewRequest("POST", constant.CwsmPrefix+"/cluster/cluster1/apts/scclinspection", body)
	Expect(err == nil).To(Equal(true))
	request.Header.Add("Content-Type", "application/json")

	response := httptest.NewRecorder()
	beego.BeeApp.Handlers.ServeHTTP(response, request)
	return response
}

var _ = Describe("test for activate scclinspection", func() {
	var fp ActivateScclinspectionPatches
	BeforeEach(func() {
		fp.EvaFakePatches()
	})

	Context("activate scclinspection", func() {
		It("activate scclinspection unmarshaling failed", func() {
			response := scclinspectionEvaSubscription(`{"aaa":111"}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		It("activate scclinspection, Return 200 When activate Succeed", func() {
			response := scclinspectionEvaSubscription(`{"inspectType": "BRCCL","cclType":"allreduce","command":"-g 1 -c 0 -b 512 -e 800m -n 100 -t 1 -w 10","nodeList":["test1", "test2"],"gpuPerNode":4,"testScene":"cluster","outAlgbw":0,"outBusbw":0,"inAlgbw":0,"inBusbw":0}`)
			Expect(response.Code).To(Equal(http.StatusOK))
		})
	})

	/* Started by AICoder, pid:54236a26f8cc4f1080aefdc7ddc5a4b9 */
	It("activate scclinspection post to wsm fail", func() {
		fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
			map[string][]string, int, error) {
			return nil, nil, 0, errors.New(`{"err":"activate scclinspection info error"}`)
		}))
		response := scclinspectionEvaSubscription(`{"inspectType": "BRCCL","cclType":"allreduce","command":"-g 1 -c 0 -b 512 -e 800m -n 100 -t 1 -w 10","nodeList":["test1", "test2"],"gpuPerNode":4,"testScene":"cluster","outAlgbw":0,"outBusbw":0,"inAlgbw":0,"inBusbw":0}`)
		Expect(response.Code).To(Equal(http.StatusBadRequest))
		Expect(response.Body.String()).To(MatchJSON(`{"message":"activate scclinspection info error"}`))
	})

	Context("activate scclinspection failed because of JSON unmarshal error", func() {
		BeforeEach(func() {
			fp.AddPatches(gomonkey.ApplyFunc(wsm.PostToWsm, func(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte,
				map[string][]string, int, error) {
				return []byte(`{"invalid_json`), nil, http.StatusOK, nil
			}))
		})

		It("activate scclinspection fails due to JSON unmarshal error", func() {
			response := scclinspectionEvaSubscription(`{"inspectType": "BRCCL","cclType":"allreduce","command":"-g 1 -c 0 -b 512 -e 800m -n 100 -t 1 -w 10","nodeList":["test1", "test2"],"gpuPerNode":4,"testScene":"cluster","outAlgbw":0,"outBusbw":0,"inAlgbw":0,"inBusbw":0}`)
			Expect(response.Code).To(Equal(http.StatusBadRequest))
		})

		AfterEach(func() {
			fp.CleanPatches()
		})
	})
	/* Ended by AICoder, pid:54236a26f8cc4f1080aefdc7ddc5a4b9 */

	AfterEach(func() {
		fp.CleanPatches()
	})
})
