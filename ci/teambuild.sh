#!/bin/bash

if [ ! $WORKSPACE ]; then 
    WORKSPACE=`pwd`/../..
    export WORKSPACE
fi
serviceZipName=cwsm
binName=cwsm
execute_path=`dirname $0`
curPwdPath=`cd $execute_path || exit; pwd`
verpath=$curPwdPath/../../version;mkdir -p ${verpath:?}
mainPath=$curPwdPath/..
appPath=$curPwdPath/../app
if [ ! $verName ];then verName=V8.23.40.02;fi
echo "Init curPwdPath "$curPwdPath
echo "Init mainPath "$mainPath
service_name=$serviceZipName
base_image_version_file=$WORKSPACE/config/Director/base_image_version.info

buildtime=$(date +%Y%m%d%H%M)
version_time_stamp=$verName.n$buildtime

chart_version=$(echo "$version_time_stamp"|awk -F"V" '{print $2}'|awk -F"." '{print $1"."$2"."$3"-"$4"-"$5}')
echo "${chart_version}"

rm -rf ${verpath:?}/*

initEnv(){
    . ./run_config.sh
}

copyConf(){
    if [ -d "$mainPath/conf" ]; then
        tar -zcvf $appPath/$serviceZipName/$serviceZipName/conf.tar.gz $mainPath/conf
        echo "Info: generate $appPath/$serviceZipName/$serviceZipName/conf.tar.gz"
    else
        echo "Error: generate conf.tar.gz fail"
    fi
}

buildBin(){
    echo "Info: begin to build $binName in $buildtime"
    currentPath=$(pwd)
    echo "Info: ci current path $currentPath"
    if ! [ -d $verpath/$serviceZipName ]; then
        mkdir -p $verpath/$serviceZipName
    else
        cd $verpath/$serviceZipName || exit;rm -rf *
    fi
    cd $mainPath || exit
    rm -f $appPath/$serviceZipName/$serviceZipName/$binName
    echo $appPath/$serviceZipName/$serviceZipName/$binName
    LDFLAGS="-w -s"
    go build -ldflags "${LDFLAGS}" -o $appPath/$serviceZipName/$serviceZipName/$binName main.go
    if [ $? -eq 0 ]; then
        echo "Info: go build OK"
        echo "" > empty_buildinfo
        objcopy --update-section .go.buildinfo=empty_buildinfo $appPath/$serviceZipName/$serviceZipName/$binName
    else
        echo "Error: go build error! Please run 'go build' before commit codes"
        exit 1
    fi
}

replaceGoBaseImageVersion() {
    dockerFilePath=$1
    local dir=$2
    echo "work space list : "
    ls -al $WORKSPACE
    echo "version file is -- : ${base_image_version_file}"
    if [ -f ${base_image_version_file} ]; then
      echo "exist version config file"
      echo "$(cat ${base_image_version_file})"
    fi
    echo "docker file path is :$dockerFilePath"
    if [ -f $dockerFilePath ]; then
      echo "exist docker file"
    fi

    local gobase_version_key="gobase:"
    case "$dir" in
        "x64")
            gobase_version_key="gobase_tcf:"
            ;;
		"riscv64")
            gobase_version_key="gobase_riscv:"
            ;;

    esac
    image_no=$(cat ${base_image_version_file} | grep ${gobase_version_key} | awk -F':' '{print $2}')
    echo "gobase image number is: ${image_no}"
    sed -i "s/gobase:.*/gobase:${image_no}/" $dockerFilePath

    echo "$(cat $dockerFilePath)"
}

replaceGoBaseImageAarchVersion() {
    dockerFilePath=$1
    local dir=$2
    echo "work space list : "
    ls -al $WORKSPACE
    echo "version file is -- : ${base_image_version_file}"
    if [ -f ${base_image_version_file} ]; then
      echo "exist version config file"
      echo "$(cat ${base_image_version_file})"
    fi
    echo "docker file path is :$dockerFilePath"
    if [ -f $dockerFilePath ]; then
      echo "exist docker file"
    fi

    local gobase_version_key="gobase:"
    case "$dir" in
        "aarch64")
            gobase_version_key="gobase_arm:"
            ;;
    esac
    image_no=$(cat ${base_image_version_file} | grep ${gobase_version_key} | awk -F':' '{print $2}')
    echo "gobase image number is: ${image_no}"
    sed -i "s/gobase:.*/gobase:${image_no}/" $dockerFilePath

    echo "$(cat $dockerFilePath)"
}

replaceBackupSeedImageVersion() {
    dockerFilePath=$1
    echo "work space list : "
    ls -al $WORKSPACE
    echo "version file is -- : ${base_image_version_file}"
    if [ -f ${base_image_version_file} ]; then
      echo "exist version config file"
      echo "$(cat ${base_image_version_file})"
    fi
    echo "docker file path is :$dockerFilePath"
    if [ -f $dockerFilePath ]; then
      echo "exist docker file"
    fi
    image_no=$(cat ${base_image_version_file} | grep backupseed: | awk -F':' '{print $2}')
    echo "backupseed image number is: ${image_no}"
    sed -i "s/backupseed:.*/backupseed:${image_no}/" $dockerFilePath
    echo "$(cat $dockerFilePath)"
}

replaceBackupBaseImagerVersion(){
    local blueprintPath=$1
    ls -al $WORKSPACE
    echo "version file is -- : ${base_image_version_file}"
    if [ -f ${base_image_version_file} ]; then
      echo "exist version config file"
      echo "$(cat ${base_image_version_file})"
    fi
    image_no=$(cat ${base_image_version_file} | grep backup-base: | awk -F':' '{print $2}')
    echo "backup-base image number is: ${image_no}"
    sed -i "s/backup-base:V5/backup-base:${image_no}/" $blueprintPath
}

packAndUpload(){
    dir=$1
    rm -rf $verpath/app
    echo ${version_time_stamp}
    
    if [ "$dir" == "aarch64" ]; then
        cp $mainPath/../../bin/linux_arm64/* $appPath/cwsm/cwsm
    fi

    cp -rf $appPath $verpath
 
    cd $curPwdPath/../ || exit
    tar -cvf conf.tar conf

    mv $curPwdPath/../conf.tar  $verpath/app/$serviceZipName/$serviceZipName/$serviceZipName/
    echo ${version_time_stamp}

    if [ "$dir" == "aarch64" ]; then
        dockerFilePath="$verpath/app/$serviceZipName/$serviceZipName/Dockerfile.arm"
    else
        dockerFilePath="$verpath/app/$serviceZipName/$serviceZipName/Dockerfile"
    fi

    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $verpath/app/$serviceZipName.spd
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $currentPath/../tcf/$serviceZipName.spd
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $verpath/app/$serviceZipName/install.sh
    eval sed -i 's/version_will_replaced/${chart_version}/g' $verpath/app/$serviceZipName/install.sh
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $verpath/app/$serviceZipName/blueprint/director_cwsm_service/template_$serviceZipName.json
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $verpath/app/$serviceZipName/blueprint/director_cwsm_chart/$serviceZipName/Chart.yaml
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $verpath/app/$serviceZipName/blueprint/director_cwsm_chart/$serviceZipName/values.yaml
    eval sed -i 's/Version_will_replaced/${chart_version}/g' $verpath/app/$serviceZipName/blueprint/director_cwsm_chart/$serviceZipName/Chart.yaml
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $currentPath/../tcf/cwsm.spd.ext
    eval sed -i 's/appVersion_will_replaced/${version_time_stamp}/g' $currentPath/../tcf/install.sh
    eval sed -i 's/${serviceZipName}_version/${version_time_stamp}/g' $currentPath/../tcf/$serviceZipName/blueprint/template_$serviceZipName.json
    eval sed -i 's/chartVersion/${chart_version}/g' $currentPath/../tcf/install.sh
    eval sed -i 's/chartVersion/${chart_version}/g' $currentPath/../tcf/$serviceZipName/blueprint/director_cwsm_chart/$serviceZipName/Chart.yaml

    if [ "$dir" == "aarch64" ]; then
        replaceGoBaseImageAarchVersion $dockerFilePath "$dir"
    else
        replaceGoBaseImageVersion $dockerFilePath "$dir"
    fi
    replaceBackupBaseImagerVersion $verpath/app/$serviceZipName/blueprint/director_cwsm_service/template_cwsm.json

    if [ "$dir" == "aarch64" ]; then
        rm -f "$verpath/app/$serviceZipName/$serviceZipName/Dockerfile"
        mv "$verpath/app/$serviceZipName/$serviceZipName/Dockerfile.arm" "$verpath/app/$serviceZipName/$serviceZipName/Dockerfile"
    else
        rm -f "$verpath/app/$serviceZipName/$serviceZipName/Dockerfile.arm"
    fi

    cd $verpath/app/$serviceZipName/blueprint/director_cwsm_chart || exit
    tar zcvf $serviceZipName-${chart_version}.tgz $serviceZipName
    rm -rf $serviceZipName
    cd $verpath/app/ || exit
    tar -zcvf $serviceZipName-$version_time_stamp.tar.gz $serviceZipName
    rm -rf $serviceZipName
    cd $verpath || exit
    tar -cvf $serviceZipName.tar app
    ls -l $verpath

    if [[ -e $verpath/$serviceZipName.tar ]]; then
        echo "Info: copy to $verpath success!"
    else
        echo "Error: $verpath/$serviceZipName.tar is not exist. no need upload to $verpath"
        exit 1
    fi
}

GCC_LINARO_PATH=$mainPath/gcc-linaro-7.5.0-2019.12-x86_64_aarch64-linux-gnu/bin

make_arm() {
  echo "---------- Start build cwsm arm-----------"
  cd $mainPath || exit

  curl -H 'X-JFrog-Art-Api:AKCp5Z3fy8xyiF6kfFX4ZdMUEqLRLjDzJGJ2vnMd8FEqXBw7aPfTe9feSUthvT83XWHCQPfG2' -O \"https://artnj.zte.com.cn/artifactory/ict-ops-release-generic/gcc/gcc-linaro.tar\"

  tar -xvf $mainPath/gcc-linaro.tar -C $mainPath

  echo $GCC_LINARO_PATH/aarch64-linux-gnu-gcc
  CC=$GCC_LINARO_PATH/aarch64-linux-gnu-gcc GOARM=7 GOARCH=arm64 GOOS=linux GODEBUG=madvdontneed=1 go install -buildvcs=false -ldflags "-s -w" -v $MY_PROJECT

  echo "" > empty_buildinfo
  yum install -y binutils-aarch64-linux-gnu
  aarch64-linux-gnu-objcopy --update-section .go.buildinfo=empty_buildinfo $mainPath/../../bin/linux_arm64/cwsm
  rm -f empty_buildinfo
  echo "---------- End build cwsm arm-----------"
}

make_riscv() {
    echo "---------- Start build cwsm riscv-----------"
    cd $mainPath || exit
    
    rm -f $appPath/$serviceZipName/$serviceZipName/$binName
    #curl -H 'X-JFrog-Art-Api:AKCp8jQTbCbK7xD6a3W3Q97rZiAqS8n3rHNH2u5tvBjunrD1HLyJyr1S5YzUG3CodgqQzNFvT' -O "https://artxa.zte.com.cn:443/artifactory/idn-alpha-generic/DexCloud/V3.25.10.06RC2/versions/DexCloud-RISCV3.25.10.06RC2/DexCloud_BaseImages/base-images/02_gobase/gobase-V3.25.10.06.n2502260904.tar.gz"
    LDFLAGS="-w -s"
    GOARCH=riscv64 GOOS=linux GODEBUG=madvdontneed=1 go build -buildvcs=false -ldflags "${LDFLAGS}" -o $appPath/$serviceZipName/$serviceZipName/$binName main.go
    if [ $? -eq 0 ]; then
        echo "Info: go build riscv64 OK"
        echo $curPwdPath
        echo "" > empty_buildinfo
        $curPwdPath/riscv64-unknown-linux-gnu-objcopy --update-section .go.buildinfo=empty_buildinfo $appPath/$serviceZipName/$serviceZipName/$binName
    else
        echo "Error: go build error! Please run 'go build' before commit codes"
        exit 1
    fi

    rm -f empty_buildinfo
    echo "---------- End build cwsm riscv-----------"
}

handle_aarch64() {
    echo "++++++++++++++  aarch64 ++++++++++++++++"
    export GOARCH=arm64
    make_arm
    copyConf
    packAndUpload "$dir"
    cp -rf $verpath/$service_name.tar $verpath/aarch64/
}

handle_riscv64() {
    echo "++++++++++++++  riscv64 ++++++++++++++++"
    export GOARCH=riscv64
    make_riscv
    copyConf
    packAndUpload "$dir"
    cp -rf $verpath/$service_name.tar $verpath/riscv64/
}

distribution_version_package() {

    #mkdir -p $verpath/cpaas/app/$service_name
    mkdir -p $verpath/x64/$service_name
    mkdir -p $verpath/aarch64/$service_name
	mkdir -p $verpath/riscv64/$service_name
    update_go_dependencies
    cd $verpath || exit
    for dir in *; do
        case "$dir" in
        "x64")
            echo "++++++++++++++  x64 ++++++++++++++++"
            export GOARCH=amd64
            buildBin
            copyConf
            packAndUpload "$dir"
            cp -rf $verpath/app/* $verpath/$dir/$service_name
            cd $verpath/$dir || exit
            tar -cvf $service_name.tar $service_name
            ;;
        "aarch64")
            echo "++++++++++++++  aarch64 ++++++++++++++++"
            handle_aarch64
            ;;
        "riscv64")
            handle_riscv64
            ;;
        esac
    done
    rm -rf $verpath/app
    rm -rf $verpath/cwsm*

}

update_go_dependencies() {
    echo "Info: Updating Go dependencies..."
    
    cd $mainPath || exit
    
    echo "Info: Removing go.sum file..."
    rm -f go.sum
    
    echo "Info: Cleaning module cache..."
    go clean -modcache
    
    echo "Info: Updating dependencies with go mod tidy..."
    if ! go mod tidy; then
        echo "Error: Failed to update dependencies"
        exit 1
    fi
    
    echo "Info: Successfully updated Go dependencies"
}

initEnv
distribution_version_package