module cwsm

go 1.22.5

require (
	github.com/Shopify/sarama v1.38.1
	github.com/agiledragon/gomonkey/v2 v2.13.0
	github.com/beego/beego/v2 v2.3.4
	github.com/beego/i18n v0.0.0-20161101132742-e9308947f407
	github.com/go-redis/redis v6.15.9+incompatible
	github.com/gofrs/uuid v4.4.0+incompatible
	github.com/google/uuid v1.6.0
	github.com/lib/pq v1.10.9
	github.com/onsi/ginkgo v1.16.5
	github.com/onsi/gomega v1.13.0
	github.com/pkg/errors v0.9.1
	github.com/smartystreets/goconvey v1.8.1
	github.com/tidwall/gjson v1.14.4
	gitlab.zte.com.cn/oes/dexadf v1.2110.60200
	gopkg.in/olivere/elastic.v6 v6.2.31
	k8s.io/api v0.24.2
	k8s.io/apimachinery v0.24.2
	zte.com.cn/cms/crmX/commontools v1.9.9
	zte.com.cn/cms/crmX/commontools-base v1.0.1
	zte.com.cn/cms/crmX/commontools-msb v1.0.1
)

require (
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/eapache/go-resiliency v1.6.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230111030713-bf00bc1b83b6 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/fsnotify/fsnotify v1.7.0
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/klauspost/compress v1.15.14 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/config v1.4.0 // indirect
	go.uber.org/dig v1.17.1 // indirect
	go.uber.org/fx v1.22.1
	go.uber.org/multierr v1.10.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/lint v0.0.0-20210508222113-6edffad5e616 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	zte.com.cn/dexcloud/go-mars v1.3.2
)

require (
	github.com/Unknwon/goconfig v1.0.0 // indirect
	github.com/agiledragon/gomonkey v2.1.0+incompatible // indirect
	github.com/asaskevich/govalidator v0.0.0-20230301143203-a9d515a09cc2 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bitly/go-simplejson v0.5.1 // indirect
	github.com/cenkalti/backoff/v4 v4.2.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/emmansun/gmsm v0.26.1 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-openapi/analysis v0.23.0 // indirect
	github.com/go-openapi/errors v0.22.0 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/jsonreference v0.21.0 // indirect
	github.com/go-openapi/loads v0.22.0 // indirect
	github.com/go-openapi/runtime v0.28.0 // indirect
	github.com/go-openapi/spec v0.21.0 // indirect
	github.com/go-openapi/strfmt v0.23.0 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/go-openapi/validate v0.24.0 // indirect
	github.com/go-ozzo/ozzo-validation v3.5.0+incompatible // indirect
	github.com/go-ozzo/ozzo-validation/v4 v4.3.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/gofuzz v1.1.0 // indirect
	github.com/google/gops v0.3.28 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/gorilla/websocket v1.5.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.19.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.3 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/mcuadros/go-defaults v1.2.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/muesli/cache2go v0.0.0-20201208071950-e3e970b4892f // indirect
	github.com/nxadm/tail v1.4.8 // indirect
	github.com/oklog/ulid v1.3.1 // indirect
	github.com/olivere/elastic v6.2.37+incompatible // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/pierrec/lz4/v4 v4.1.17 // indirect
	github.com/prometheus/client_golang v1.19.1 // indirect
	github.com/prometheus/client_model v0.5.0 // indirect
	github.com/prometheus/common v0.48.0 // indirect
	github.com/prometheus/procfs v0.12.0 // indirect
	github.com/segmentio/kafka-go v0.4.38 // indirect
	github.com/shiena/ansicolor v0.0.0-20200904210342-c7312218db18 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/timandy/routine v1.1.3 // indirect
	github.com/tylerb/gls v0.0.0-20150407001822-e606233f194d // indirect
	go.mongodb.org/mongo-driver v1.14.0 // indirect
	go.opentelemetry.io/otel v1.24.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.24.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.24.0 // indirect
	go.opentelemetry.io/otel/metric v1.24.0 // indirect
	go.opentelemetry.io/otel/sdk v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	go.opentelemetry.io/proto/otlp v1.1.0 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	golang.org/x/crypto v0.32.0 // indirect
	golang.org/x/net v0.27.0 // indirect
	golang.org/x/sync v0.7.0 // indirect
	golang.org/x/sys v0.29.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240227224415-6ceb2ff114de // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240227224415-6ceb2ff114de // indirect
	google.golang.org/grpc v1.63.0 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
	gopkg.in/h2non/gentleman.v2 v2.0.5 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	k8s.io/klog/v2 v2.120.1 // indirect
	k8s.io/utils v0.0.0-20220210201930-3a6ce19ff2f9 // indirect
	sigs.k8s.io/json v0.0.0-20211208200746-9f7c6b3444d2 // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.2.1 // indirect
	zte.com.cn/oes/dexadf v1.2440.10000 // indirect
)

replace (
	github.com/cncf/xds/go => github.com/cncf/xds/go v0.0.0-20230607035331-e9ce68804cb4
	github.com/emicklei/go-restful => github.com/emicklei/go-restful/v3 v3.10.0
	github.com/gogo/protobuf => github.com/gogo/protobuf v1.3.2
	github.com/labstack/echo => github.com/labstack/echo/v4 v4.9.1
	github.com/prometheus/client_golang => github.com/prometheus/client_golang v1.19.1
	github.com/prometheus/common => github.com/prometheus/common v0.32.1
	github.com/vektah/gqlparser => github.com/vektah/gqlparser/v2 v2.5.16
	go.mongodb.org/mongo-driver => go.mongodb.org/mongo-driver v1.10.1
	golang.org/x/crypto => golang.org/x/crypto v0.32.0
	golang.org/x/net => golang.org/x/net v0.34.0
	golang.org/x/text => golang.org/x/text v0.3.8
	k8s.io/client-go => k8s.io/client-go v0.24.2
	k8s.io/utils => k8s.io/utils v0.0.0-20230726121419-3b25d923346b
)

exclude (
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/form3tech-oss/jwt-go v3.2.2+incompatible
	github.com/form3tech-oss/jwt-go v3.2.3+incompatible
	github.com/getkin/kin-openapi v0.76.0
	github.com/golang-jwt/jwt v3.2.2+incompatible
	github.com/golang-jwt/jwt/v4 v4.5.0
	github.com/golang/glog v0.0.0-20160126235308-23def4e6c14b
	github.com/golang/glog v1.0.0
	github.com/golang/glog v1.1.0
	google.golang.org/genproto v0.0.0-20180817151627-c66870c02cf8
	google.golang.org/genproto v0.0.0-20190307195333-5fe7a883aa19
	google.golang.org/genproto v0.0.0-20190418145605-e7d98fc518a7
	google.golang.org/genproto v0.0.0-20190425155659-357c62f0e4bb
	google.golang.org/genproto v0.0.0-20190502173448-54afdca5d873
	google.golang.org/genproto v0.0.0-20190801165951-fa694d86fc64
	google.golang.org/genproto v0.0.0-20190819201941-24fa4b261c55
	google.golang.org/genproto v0.0.0-20190911173649-1774047e7e51
	google.golang.org/genproto v0.0.0-20191108220845-16a3f7862a1a
	google.golang.org/genproto v0.0.0-20191115194625-c23dd37a84c9
	google.golang.org/genproto v0.0.0-20191216164720-4f79533eabd1
	google.golang.org/genproto v0.0.0-20191230161307-f3c370f40bfb
	google.golang.org/genproto v0.0.0-20200115191322-ca5a22157cba
	google.golang.org/genproto v0.0.0-20200122232147-0452cf42e150
	google.golang.org/genproto v0.0.0-20200204135345-fa8e72b47b90
	google.golang.org/genproto v0.0.0-20200212174721-66ed5ce911ce
	google.golang.org/genproto v0.0.0-20200224152610-e50cd9704f63
	google.golang.org/genproto v0.0.0-20200228133532-8c2c7df3a383
	google.golang.org/genproto v0.0.0-20200305110556-506484158171
	google.golang.org/genproto v0.0.0-20200312145019-da6875a35672
	google.golang.org/genproto v0.0.0-20200331122359-1ee6d9798940
	google.golang.org/genproto v0.0.0-20200430143042-b979b6f78d84
	google.golang.org/genproto v0.0.0-20200511104702-f5ebc3bea380
	google.golang.org/genproto v0.0.0-20200513103714-09dca8ec2884
	google.golang.org/genproto v0.0.0-20200515170657-fc4c6c6a6587
	google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013
	google.golang.org/genproto v0.0.0-20200618031413-b414f8b61790
	google.golang.org/genproto v0.0.0-20200729003335-053ba62fc06f
	google.golang.org/genproto v0.0.0-20200804131852-c06518451d9c
	google.golang.org/genproto v0.0.0-20200825200019-8632dd797987
	google.golang.org/genproto v0.0.0-20200904004341-0bd0a958aa1d
	google.golang.org/genproto v0.0.0-20201019141844-1ed22bb0c154
	google.golang.org/genproto v0.0.0-20201109203340-2640f1f9cdfb
	google.golang.org/genproto v0.0.0-20201201144952-b05cb90ed32e
	google.golang.org/genproto v0.0.0-20201210142538-e3217bee35cc
	google.golang.org/genproto v0.0.0-20201214200347-8c77b98c765d
	google.golang.org/genproto v0.0.0-20210108203827-ffc7fda8c3d7
	google.golang.org/genproto v0.0.0-20210222152913-aa3ee6e6a81c
	google.golang.org/genproto v0.0.0-20210226172003-ab064af71705
	google.golang.org/genproto v0.0.0-20210303154014-9728d6b83eeb
	google.golang.org/genproto v0.0.0-20210310155132-4ce2db91004e
	google.golang.org/genproto v0.0.0-20210319143718-93e7006c17a6
	google.golang.org/genproto v0.0.0-20210329143202-679c6ae281ee
	google.golang.org/genproto v0.0.0-20210402141018-6c239bbf2bb1
	google.golang.org/genproto v0.0.0-20210513213006-bf773b8c8384
	google.golang.org/genproto v0.0.0-20210602131652-f16073e35f0c
	google.golang.org/genproto v0.0.0-20210604141403-392c879c8b08
	google.golang.org/genproto v0.0.0-20210608205507-b6d2f5bf0d7d
	google.golang.org/genproto v0.0.0-20210624195500-8bfb893ecb84
	google.golang.org/genproto v0.0.0-20210713002101-d411969a0d9a
	google.golang.org/genproto v0.0.0-20210716133855-ce7ef5c701ea
	google.golang.org/genproto v0.0.0-20210728212813-7823e685a01f
	google.golang.org/genproto v0.0.0-20210805201207-89edb61ffb67
	google.golang.org/genproto v0.0.0-20210813162853-db860fec028c
	google.golang.org/genproto v0.0.0-20210821163610-241b8fcbd6c8
	google.golang.org/genproto v0.0.0-20210828152312-66f60bf46e71
	google.golang.org/genproto v0.0.0-20210831024726-fe130286e0e2
	google.golang.org/genproto v0.0.0-20210903162649-d08c68adba83
	google.golang.org/genproto v0.0.0-20210909211513-a8c4777a87af
	google.golang.org/genproto v0.0.0-20210924002016-3dee208752a0
	google.golang.org/genproto v0.0.0-20211118181313-81c1377c94b1
	google.golang.org/genproto v0.0.0-20211206160659-862468c7d6e0
	google.golang.org/genproto v0.0.0-20211208223120-3a66f561d7aa
	google.golang.org/genproto v0.0.0-20211221195035-429b39de9b1c
	google.golang.org/genproto v0.0.0-20220126215142-9970aeb2e350
	google.golang.org/genproto v0.0.0-20220207164111-0872dc986b00
	google.golang.org/genproto v0.0.0-20220218161850-94dd64e39d7c
	google.golang.org/genproto v0.0.0-20220222213610-43724f9ea8cf
	google.golang.org/genproto v0.0.0-20220304144024-325a89244dc8
	google.golang.org/genproto v0.0.0-20220310185008-1973136f34c6
	google.golang.org/genproto v0.0.0-20220324131243-acbaeb5b85eb
	google.golang.org/genproto v0.0.0-20220329172620-7be39ac1afc7
	google.golang.org/genproto v0.0.0-20220407144326-9054f6ed7bac
	google.golang.org/genproto v0.0.0-20220413183235-5e96e2839df9
	google.golang.org/genproto v0.0.0-20220414192740-2d67ff6cf2b4
	google.golang.org/genproto v0.0.0-20220421151946-72621c1f0bd3
	google.golang.org/genproto v0.0.0-20220429170224-98d788798c3e
	google.golang.org/genproto v0.0.0-20220502173005-c8bf987b8c21
	google.golang.org/genproto v0.0.0-20220505152158-f39f71e6c8f3
	google.golang.org/genproto v0.0.0-20220518221133-4f43b3371335
	google.golang.org/genproto v0.0.0-20220523171625-347a074981d8
	google.golang.org/genproto v0.0.0-20220608133413-ed9918b62aac
	google.golang.org/genproto v0.0.0-20220616135557-88e70c0c3a90
	google.golang.org/genproto v0.0.0-20220617124728-180714bec0ad
	google.golang.org/genproto v0.0.0-20220624142145-8cd45d7dbd1f
	google.golang.org/genproto v0.0.0-20220628213854-d9e0b6570c03
	google.golang.org/genproto v0.0.0-20220722212130-b98a9ff5e252
	google.golang.org/genproto v0.0.0-20220801145646-83ce21fca29f
	google.golang.org/genproto v0.0.0-20220815135757-37a418bb8959
	google.golang.org/genproto v0.0.0-20220817144833-d7fd3f11b9b1
	google.golang.org/genproto v0.0.0-20220822174746-9e6da59bd2fc
	google.golang.org/genproto v0.0.0-20220829144015-23454907ede3
	google.golang.org/genproto v0.0.0-20220829175752-36a9c930ecbf
	google.golang.org/genproto v0.0.0-20220913154956-18f8339a66a5
	google.golang.org/genproto v0.0.0-20220914142337-ca0e39ece12f
	google.golang.org/genproto v0.0.0-20220915135415-7fd63a7952de
	google.golang.org/genproto v0.0.0-20220916172020-2692e8806bfa
	google.golang.org/genproto v0.0.0-20220919141832-68c03719ef51
	google.golang.org/genproto v0.0.0-20220920201722-2b89144ce006
	google.golang.org/genproto v0.0.0-20220926165614-551eb538f295
	google.golang.org/genproto v0.0.0-20220926220553-6981cbe3cfce
	google.golang.org/genproto v0.0.0-20221010155953-15ba04fc1c0e
	google.golang.org/genproto v0.0.0-20221014173430-6e2ab493f96b
	google.golang.org/genproto v0.0.0-20221014213838-99cd37c6964a
	google.golang.org/genproto v0.0.0-20221024153911-1573dae28c9c
	google.golang.org/genproto v0.0.0-20221024183307-1bc688fe9f3e
	google.golang.org/genproto v0.0.0-20221027153422-115e99e71e1c
	google.golang.org/genproto v0.0.0-20221109142239-94d6d90a7d66
	google.golang.org/genproto v0.0.0-20221114212237-e4508ebdbee1
	google.golang.org/genproto v0.0.0-20221117204609-8f9c96812029
	google.golang.org/genproto v0.0.0-20221118155620-16455021b5e6
	google.golang.org/genproto v0.0.0-20221201164419-0e50fba7f41c
	google.golang.org/genproto v0.0.0-20221201204527-e3fa12d562f3
	google.golang.org/genproto v0.0.0-20221202195650-67e5cbc046fd
	google.golang.org/genproto v0.0.0-20221227171554-f9683d7f8bef
	google.golang.org/genproto v0.0.0-20230110181048-76db0878b65f
	google.golang.org/genproto v0.0.0-20230112194545-e10362b5ecf9
	google.golang.org/genproto v0.0.0-20230113154510-dbe35b8444a5
	google.golang.org/genproto v0.0.0-20230123190316-2c411cf9d197
	google.golang.org/genproto v0.0.0-20230124163310-31e0e69b6fc2
	google.golang.org/genproto v0.0.0-20230125152338-dcaf20b6aeaa
	google.golang.org/genproto v0.0.0-20230127162408-596548ed4efa
	google.golang.org/genproto v0.0.0-20230209215440-0dfe4f8abfcc
	google.golang.org/genproto v0.0.0-20230216225411-c8e22ba71e44
	google.golang.org/genproto v0.0.0-20230222225845-10f96fb3dbec
	google.golang.org/genproto v0.0.0-20230223222841-637eb2293923
	google.golang.org/genproto v0.0.0-20230303212802-e74f57abe488
	google.golang.org/genproto v0.0.0-20230306155012-7f2fa6fef1f4
	google.golang.org/genproto v0.0.0-20230320184635-7606e756e683
	google.golang.org/genproto v0.0.0-20230323212658-478b75c54725
	google.golang.org/genproto v0.0.0-20230330154414-c0448cd141ea
	google.golang.org/genproto v0.0.0-20230331144136-dcfb400f0633
	google.golang.org/genproto v0.0.0-20230403163135-c38d8f061ccd
	google.golang.org/genproto v0.0.0-20230410155749-daa745c078e1
	google.golang.org/genproto v0.0.0-20230525234025-438c736192d0
	google.golang.org/genproto v0.0.0-20230530153820-e85fd2cbaebc
)
