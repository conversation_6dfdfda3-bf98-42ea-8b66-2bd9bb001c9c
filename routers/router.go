package routers

import (
	"cwsm/controllers"
	"cwsm/infra/constant"

	"zte.com.cn/cms/crmX/commontools-msb/interceptor"
	"zte.com.cn/cms/crmX/commontools/security"

	otcpsm "zte.com.cn/cms/crmX/commontools/otcp/sm"

	beego "github.com/beego/beego/v2/server/web"
)

func init() {
	beego.BeeApp.Handlers.InsertFilter("/*", beego.BeforeExec, security.HandleSQLInjection)
	beego.BeeApp.Handlers.InsertFilter("/*", beego.BeforeExec, security.HandleXSSAttackREQ)
	beego.BeeApp.Handlers.InsertFilter("/*", beego.AfterExec, security.HandleXSSAttackRSP)
	beego.BeeApp.Handlers.InsertFilter("/*", beego.BeforeExec, otcpsm.OesSmAuthFilterEx)
	initInspectiontaskRouter()
	initScclinspectionRouter()
	initModelinspectionRouter()
	initHealthRouter()
	initRdmaRouter()
	initVcjobEventsRouter()
	initAcceleratorRouter()
	initInspectRouter()
	initSwrRouter()
	debugRouter()
	beego.InsertFilter("/*", beego.BeforeRouter, interceptor.ApiGatewayInterceptor, beego.WithReturnOnOutput(false))
}

func initInspectiontaskRouter() {
	initInspectiontaskNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/diagtask", &controllers.InspectiontaskController{}),
		beego.NSRouter("/inspectiontask", &controllers.InspectiontaskController{}),
		beego.NSRouter("/inspectiontask/stop", &controllers.InspectiontaskController{}, "delete:Delete"),
	)
	beego.AddNamespace(initInspectiontaskNs)
}

func initScclinspectionRouter() {
	initScclinspectionNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/scclinspection", &controllers.ScclinspectionController{}),
		beego.NSRouter("/scclinspection/stop", &controllers.ScclinspectionController{}, "post:Stop"),
	)
	beego.AddNamespace(initScclinspectionNs)
}

func initModelinspectionRouter() {
	initModelinspectionNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/modelinspection", &controllers.ModelinspectionController{}),
		beego.NSRouter("/modelinspection/delete/?:taskId", &controllers.ModelinspectionController{}, "delete:Delete"),
		beego.NSRouter("/modelinspection/stop/?:taskId", &controllers.ModelinspectionController{}, "delete:Stop"),
		beego.NSRouter("/modelinspectionconfig", &controllers.ModelinspectionController{}, "get:GetModelinspectionConfig"),
		beego.NSRouter("/clrdmainspectionconfig", &controllers.ModelinspectionController{}, "get:GetClRdmaInspectionConfig"),
		beego.NSRouter("/namespace", &controllers.ModelinspectionController{}, "get:GetNamespace"),
		beego.NSRouter("/modelinspectionconfig", &controllers.ModelinspectionController{}, "post:PostModelinspectionConfig"),
		beego.NSRouter("/clrdmainspectionconfig", &controllers.ModelinspectionController{}, "post:PostClRdmaInspectionConfig"),
		beego.NSRouter("/namespace", &controllers.ModelinspectionController{}, "post:PostNamespace"),
		beego.NSRouter("/datasets", &controllers.ModelinspectionController{}, "get:GetDataSets"),
		beego.NSRouter("/cclinspectionconfig", &controllers.ModelinspectionController{}, "get:GetCclinspectionconfig"),
		beego.NSRouter("/cclinspectionconfig", &controllers.ModelinspectionController{}, "post:PostCclinspectionconfig"),
	)
	beego.AddNamespace(initModelinspectionNs)
}

func initRdmaRouter() {
	initRdmaNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/rdma", &controllers.RdmaController{}),
		beego.NSRouter("/rdmabandwidth", &controllers.RdmaController{}),
		beego.NSRouter("/queryrdmatopo", &controllers.RdmaController{}, "get:GetRdmaTopoCfg"),
		beego.NSRouter("/rdma/stop", &controllers.RdmaController{}, "post:Stop"),
	)
	beego.AddNamespace(initRdmaNs)
}

func initHealthRouter() {
	initHealthNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/healthcheck", &controllers.HealthController{}),
		beego.NSRouter("/healthcheckcfg", &controllers.HealthController{}, "get:GetHealthCheckCfg"),
		beego.NSRouter("/healthcheckgpunodes", &controllers.HealthController{}, "get:GetHealthCheckGPUNodes"),
	)
	beego.AddNamespace(initHealthNs)
}

func initVcjobEventsRouter() {
	initVcjobEventsNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/vcjobevents/:projectName/:name", &controllers.VcjobEventsController{}, "get:GetVcjobEvents"),
		beego.NSRouter("/nsvcjobevents/:projectName", &controllers.VcjobEventsController{}),
		beego.NSRouter("/vcjobs/?:projectname", &controllers.VcjobEventsController{}, "get:GetVcjobs"),
		beego.NSRouter("/faultanalacfg", &controllers.VcjobEventsController{}, "get:GetVcjobCfg"),
		beego.NSRouter("/faultanalacfg", &controllers.VcjobEventsController{}, "post:Post"),
	)
	beego.AddNamespace(initVcjobEventsNs)
}

func initAcceleratorRouter() {
	accleratorNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/acceleratordevices", &controllers.AcceleratorController{}),
		beego.NSRouter("/gpunodes", &controllers.AcceleratorController{}, "get:GetNodes"),
	)
	beego.AddNamespace(accleratorNs)
}

func initSwrRouter() {
	swrNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/swr/query/:tenant", &controllers.SwrController{}),
	)
	beego.AddNamespace(swrNs)
}

func initInspectRouter() {
	inspectNs := beego.NewNamespace(constant.CwsmPrefix+"/cluster/:clusterId/apts",
		beego.NSRouter("/inspectionplan/create", &controllers.InspectController{}),
		beego.NSRouter("/inspectionplan/:id", &controllers.InspectController{}, "get:GetOne"),
		beego.NSRouter("/inspectionplan/:id/modify", &controllers.InspectController{}, "post:Modify"),
		beego.NSRouter("/inspectionplan/:id/start", &controllers.InspectController{}, "post:Activate"),
		beego.NSRouter("/inspectionplan/list", &controllers.InspectController{}),
		beego.NSRouter("/inspectionplan/:id/planresultlist", &controllers.InspectController{}, "get:GetPlanResultList"),
		beego.NSRouter("/inspectionplan/:id/planresult/:resultid", &controllers.InspectController{}, "get:GetPlanResult"),
		beego.NSRouter("/inspectionplan/delete", &controllers.InspectController{}, "post:Delete"),
		beego.NSRouter("/inspectionplan/:id/stop", &controllers.InspectController{}, "post:Stop"),
		beego.NSRouter("/inspectionplan/:planid/planresult/delete", &controllers.InspectController{}, "post:DeleteResult"),
	)
	beego.AddNamespace(inspectNs)
}

func debugRouter() {
	beego.Router(constant.CwsmPrefix+"/debug/changeloggerlevel/?:level", &controllers.DebugController{}, "POST:SwitchLogLevel")
	beego.Router(constant.CwsmPrefix+"/debug/getdb/:tablename", &controllers.DebugController{}, "GET:GetDbInfo")
	beego.Router(constant.CwsmPrefix+"/debug/delete/:tablename/?:id", &controllers.DebugController{}, "DELETE:DeleteById")
	beego.Router(constant.CwsmPrefix+"/debug/insert/:tablename", &controllers.DebugController{}, "POST:InsertRow")
	beego.Router(constant.CwsmPrefix+"/debug/changerow/:tablename/?:id", &controllers.DebugController{}, "PATCH:ChangeRowById")

}
