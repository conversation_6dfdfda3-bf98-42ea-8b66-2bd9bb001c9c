package wsm

import (
	"cwsm/infra/authorization"
	"cwsm/infra/cwsmutils"
	"cwsm/tools/commontools/logger"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"

	"zte.com.cn/cms/crmX/commontools-base/restful"

	v1 "k8s.io/api/core/v1"
)

func ExtractIPFromURI(uri string) (string, error) {
	ipv4Regex := `(\d{1,3}\.){3}\d{1,3}`
	ipv6Regex := `\[[a-fA-F0-9:]+\]`

	re := regexp.MustCompile(fmt.Sprintf("%s|%s", ipv4Regex, ipv6Regex))
	match := re.FindString(uri)
	if match == "" {
		return "", fmt.Errorf("no valid ip address found in url")
	}
	return match, nil
}

func BuildOpenpaletteServiceReq(restfulParams authorization.OpenpaletteInfo) WsmRestfulParams {
	svcUrl := restfulParams.MsbURL + "/api/v1/services"
	header := restful.DefaultHeaders()
	if len(restfulParams.Token) > 0 {
		header["X-Auth-Token"] = restfulParams.Token
	}
	return WsmRestfulParams{URL: svcUrl, Header: header, SSL: restfulParams.SSL}
}

func GetWsmPort(restfulParams WsmRestfulParams) (string, bool) {
	body, _, rspCode, err := restful.GetMethod(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
		return "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return "", false
	}
	var data v1.ServiceList
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get accelerator data json unmarshal error: %v", err.Error())
		return "", false
	}
	for _, svc := range data.Items {
		if svc.Name == "op-aif-wsm" {
			for _, port := range svc.Spec.Ports {
				if port.Name == "op-aif-wsm-port" {
					return strconv.Itoa(int(port.NodePort)), true
				}
			}
		}
	}
	return "", true
}

func GetOpenpalette(clusterId string) (authorization.OpenpaletteInfo, error) {
	info, envId, err := authorization.GetOpenpaletteInfoFromPvrm(clusterId)
	if err != nil {
		return authorization.OpenpaletteInfo{}, err
	}
	if info.EnvType == "tcf-k8s" {
		return authorization.GetOpenpaletteInfo(clusterId, envId, info.ClusterName)
	}
	restfulParams := BuildOpenpaletteServiceReq(info)
	port, ok := GetWsmPort(restfulParams)
	if !ok {
		logger.Error("cannot get wsm port from svc")
		return authorization.OpenpaletteInfo{}, fmt.Errorf("cannot get wsm port from svc")
	}
	logger.Info("wsm port is %s", port)
	url, err := ExtractIPFromURI(info.MsbURL)
	if err != nil {
		return authorization.OpenpaletteInfo{}, fmt.Errorf("cannot extract ip from uri")
	}
	info.MsbURL = fmt.Sprintf("https://%s:%s", url, port)
	return info, nil
}

func GetOpenpaletteByCloudUuid(clouduuid string) (authorization.OpenpaletteInfo, error) {
	clusterUuid, err := authorization.GetClusterUuidFromPvrm(clouduuid)
	if err != nil {
		return authorization.OpenpaletteInfo{}, err
	}
	return GetOpenpalette(clusterUuid)
}

func GetFromWsmToGetStatus(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
	return restful.GetMethod(urlpath, reqHeaders, auth)
}

func PostToWsm(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("post to wsm body marshal fail:", err)
		return nil, nil, http.StatusInternalServerError, err
	}
	return restful.PostMethod(urlpath, reqHeaders, bodyBytes, auth)
}

func StopToWsm(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("stop to wsm body marshal fail:", err)
		return nil, nil, http.StatusInternalServerError, err
	}
	return restful.DeleteWithBodyMethod(urlpath, reqHeaders, bodyBytes, auth)
}

func DeleteToWsm(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
	return restful.DeleteMethod(urlpath, reqHeaders, auth)
}

func PutToWsm(urlpath string, reqHeaders map[string]string, body interface{}, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		logger.Errorf("put body marshal fail:", err)
		return nil, nil, http.StatusInternalServerError, err
	}
	return restful.PutMethod(urlpath, reqHeaders, bodyBytes, auth)
}

/* Started by AICoder, pid:1a9dd74f236442cb92c5d7ca3c0545e8 */
func isRestfuleMethodSuc(err error, method string, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
	if err == nil {
		return true
	}

	logger.Errorf("restful method:%s failed, err: %v, restful params: %v", method, err.Error(), restfulParams)
	if body != nil {
		cwsmutils.PrintJsonData(body)
	}
	return false
}

/* Ended by AICoder, pid:1a9dd74f236442cb92c5d7ca3c0545e8 */

/* Started by AICoder, pid:dda6370ae7a248e78d1d197c19560bf5 */
func isResponseSuc(rspCode int, body map[string]interface{}, restfulParams WsmRestfulParams) bool {
	if rspCode == http.StatusOK {
		return true
	}

	logger.Errorf("response code: %v, restful params: %v", rspCode, restfulParams)
	if body != nil {
		cwsmutils.PrintJsonData(body)
	}
	return false
}

/* Ended by AICoder, pid:dda6370ae7a248e78d1d197c19560bf5 */

/* Started by AICoder, pid:393f43b7a4ea41d3ad3a30197922c318 */
func isPostRestfuleMethodSuc(err error, method string, body interface{}, restfulParams WsmRestfulParams) bool {
	if err == nil {
		return true
	}

	logger.Errorf("restful method:%s failed, err: %v, restful params: %v", method, err.Error(), restfulParams)
	if body != nil {
		cwsmutils.PostPrintJsonData(body)
	}
	return false
}

/* Ended by AICoder, pid:393f43b7a4ea41d3ad3a30197922c318 */

/* Started by AICoder, pid:5424c31934784edaa2b9a92589cae467 */
func isPostResponseSuc(rspCode int, body interface{}, restfulParams WsmRestfulParams) bool {
	if rspCode == http.StatusOK {
		return true
	}

	logger.Errorf("response code: %v, restful params: %v", rspCode, restfulParams)
	if body != nil {
		cwsmutils.PostPrintJsonData(body)
	}
	return false
}

/* Ended by AICoder, pid:5424c31934784edaa2b9a92589cae467 */

func InspectiontaskHandler(keyswords InspectiontaskWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildInspectiontaskRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s inspectiontask build restful require failed", method)
		return nil, "", false
	}

	switch method {
	case "get":
		logger.Info("inspectiontask get from apts")
		return GetInspectiontask(restfulParams)

	case "post":
		logger.Info("inspectiontask post from apts")
		return PostInspectiontask(restfulParams, reqBody)

	case "delete":
		logger.Info("inspectiontask delete from apts")
		return DeleteInspectiontask(restfulParams)

	default:
		logger.Errorf("inspectiontask unsupport method:%s", method)
		return nil, "", false
	}
}

/* Started by AICoder, pid:9946ab3aa75540f590935f6b83046b76 */
func GetInspectiontask(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := GetFromWsmToGetStatus(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("get task report failed body: %s rspCode:%d error: %v", string(body), rspCode, err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get task report failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get task report json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	logger.Info("get task report json unmarshal body %s to data %v", string(body), data)
	return data, "", true
}

/* Ended by AICoder, pid:9946ab3aa75540f590935f6b83046b76 */

/* Started by AICoder, pid:78170ff6997140eabb2fa1f4055c6ef7 */
func PostInspectiontask(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("post task report failed body %s rspCode: %d error: %v", string(body), rspCode, err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post task report failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post task report json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	logger.Info("post task report json unmarshal body %s to data %v", string(body), data)
	return data, "", true
}

/* Ended by AICoder, pid:78170ff6997140eabb2fa1f4055c6ef7 */

/* Started by AICoder, pid:f604d533a0134e4386a610d87ffd40e2 */
func DeleteInspectiontask(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	_, _, rspCode, err := DeleteToWsm(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("delete inspection task failed, error: %v", err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("delete inspection task failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	return nil, "", isResponseSuc(rspCode, nil, restfulParams)
}

/* Ended by AICoder, pid:f604d533a0134e4386a610d87ffd40e2 */

func buildInspectiontaskRestfulReq(keyswords InspectiontaskWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts/gpuinspectiontask"
	logger.Info("url Prefix is:%s", urlPrefix)

	url := getInspectiontaskUrl(method, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getInspectiontaskUrl(method, urlPrefix string) string {
	switch method {
	case "get", "post", "delete":
		return urlPrefix

	default:
		logger.Errorf("get inspectiontask not support method:%s", method)
		return ""
	}
}

func ScclinspectionHandler(keyswords ScclinspectionWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildScclinspectionRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s scclinspection build restful req failed", method)
		return nil, "", false
	}

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetScclinspection(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostScclinspection(restfulParams, reqBody)

	case "stop":
		logger.Info("stop from apts")
		return StopScclinspection(restfulParams, nil)

	default:
		logger.Errorf("sccl inspection unsupport method:%s", method)
		return nil, "", false
	}
}

/* Started by AICoder, pid:9e7510eaa9744b069aed42a0a7ee063d */
func GetScclinspection(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := GetFromWsmToGetStatus(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("get scclinspection failed body %s rspCode:%d error: %v", string(body), rspCode, err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get scclinspection failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get scclinspection json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:9e7510eaa9744b069aed42a0a7ee063d */

/* Started by AICoder, pid:c432bb17fdc64bdbb68009c86705f69f */
func PostScclinspection(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("post scclinspection failed body %s reqBody:%d error: %v", string(body), rspCode, err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post scclinspection failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post scclinspection json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:c432bb17fdc64bdbb68009c86705f69f */

/* Started by AICoder, pid:5106ec15705148158ceeef4627bef153 */
func StopScclinspection(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	_, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("stop scclinspection failed, error: %v", err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("stop scclinspection failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	return nil, "", isResponseSuc(rspCode, nil, restfulParams)
}

/* Ended by AICoder, pid:5106ec15705148158ceeef4627bef153 */

func buildScclinspectionRestfulReq(keyswords ScclinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts/scclinspection"

	url := getScclinspectionUrl(method, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getScclinspectionUrl(method, urlPrefix string) string {
	switch method {
	case "get":
		return urlPrefix + "/" + "query"
	case "post":
		return urlPrefix + "/" + "start"
	case "stop":
		return urlPrefix + "/" + "stop"
	default:
		logger.Errorf("get sccl inspection url not support method:%s", method)
		return ""
	}
}

/* Started by AICoder, pid:sb7b871786z89c014ea60942d0b6e0227205742a */
func ModelinspectionHandler(keywords ModelinspectionWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildModelinspectionRestfulReq(keywords, method)
	if !suc {
		logger.Error("wsm %s modelinspection config build restful req failed", method)
		return nil, "", false
	}

	switch method {
	case "getcclinspectionconfig", "getmodelinspectionconfigs", "getclrdmainspectionconfigs", "getnamespace", "getdatasets", "get":
		logger.Info("getmodelinspectionconfigs from apts")
		return GetModelinspection(restfulParams)
	case "postcclinspectionconfig", "postmodelinspectionconfigs", "postclrdmainspectionconfigs", "postnamespace", "post":
		logger.Info("postmodelinspectionconfigs from apts")
		return PostModelinspection(restfulParams, reqBody)
	case "stop":
		logger.Info("stopmodelinspectionconfigs from apts")
		return DeleteModelinspection(restfulParams)
	case "delete":
		logger.Info("deletemodelinspectionconfigs from apts")
		return DeleteModelinspection(restfulParams)
	default:
		logger.Errorf("model inspection unsupport method:%s", method)
		return nil, "", false
	}
}

/* Ended by AICoder, pid:sb7b871786z89c014ea60942d0b6e0227205742a */

/* Started by AICoder, pid:b8ea9bde275a40798c884367dbeb7d39 */
func GetModelinspection(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := GetFromWsmToGetStatus(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("get model inspection failed body %s rspCode:%d error: %v", string(body), rspCode, err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("get model rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "get code status values failed", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get model inspection failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get model inspection json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:b8ea9bde275a40798c884367dbeb7d39 */

/* Started by AICoder, pid:d5747c189ec449c4b3e3d4cbf0d62975 */
func PostModelinspection(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("post model inspection failed body %s rspCode:%d error: %v", string(body), rspCode, err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("get model rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "get code status values failed", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post model inspection failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post model inspection json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:d5747c189ec449c4b3e3d4cbf0d62975 */

/* Started by AICoder, pid:a0514e952c48473f96361702d43cbe81 */
func DeleteModelinspection(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	_, _, rspCode, err := DeleteToWsm(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("delete model inspection failed, error: %v", err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("delete model inspection failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	return nil, "", isResponseSuc(rspCode, nil, restfulParams)
}

/* Ended by AICoder, pid:a0514e952c48473f96361702d43cbe81 */

func buildModelinspectionRestfulReq(keyswords ModelinspectionWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts"
	url := getModelinspectionUrl(method, keyswords, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

/* Started by AICoder, pid:pe15209772sbc67149490b613082072e2cb1fd42 */
func getModelinspectionUrl(method string, keywords ModelinspectionWsmKeywords, urlPrefix string) string {
	switch method {
	case "post", "get":
		return urlPrefix + "/" + "modelinspection"
	case "postmodelinspectionconfigs", "getmodelinspectionconfigs":
		return urlPrefix + "/" + "modelinspectionconfig"
	case "postclrdmainspectionconfigs", "getclrdmainspectionconfigs":
		return urlPrefix + "/" + "clrdmainspectionconfig"
	case "getcclinspectionconfig", "postcclinspectionconfig":
		return urlPrefix + "/" + "cclinspectionconfig"
	case "postnamespace", "getnamespace":
		return urlPrefix + "/" + "namespace"
	case "getdatasets":
		return urlPrefix + "/" + "datasets"
	case "stop":
		return urlPrefix + "/" + "modelinspection" + "/" + "stop" + "/" + keywords.TaskId
	case "delete":
		return urlPrefix + "/" + "modelinspection" + "/" + "delete" + "/" + keywords.TaskId
	default:
		logger.Errorf("get model inspection url not support method:%s", method)
		return ""
	}
}

/* Ended by AICoder, pid:pe15209772sbc67149490b613082072e2cb1fd42 */
func HealthHandler(keyswords HealthWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildHealthRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s health build restful req failed", method)
		return nil, "", false
	}

	switch method {

	case "get", "gethealthcheckcfg", "gethealthcheckgpunodescfg":
		logger.Info("get from apts")
		return GetHealth(restfulParams)

	case "post":
		logger.Info("post health handler from apts")
		return PostHealth(restfulParams, reqBody)
	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, "", false
	}
}

/* Started by AICoder, pid:41974ecd1c5d4a54926834fff8789bc7 */
func GetHealth(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.GetMethod(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("get health failed body %s error: %v", string(body), err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("get health rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("get health statusValues :%s", statusValues)
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get health failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get health json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	logger.Info("get health json unmarshal body %s to data %v", string(body), data)
	return data, "", true
}

/* Ended by AICoder, pid:41974ecd1c5d4a54926834fff8789bc7 */

/* Started by AICoder, pid:a3587fd0abc6454690fa9b8068ea5af4 */
func PostHealth(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("post health failed body %s error: %v", string(body), err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post health failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post health json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	logger.Info("post health json unmarshal body %s to data %v", string(body), data)
	return data, "", true
}

/* Ended by AICoder, pid:a3587fd0abc6454690fa9b8068ea5af4 */

func buildHealthRestfulReq(keyswords HealthWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts"

	url := getHealthUrl(method, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getHealthUrl(method, urlPrefix string) string {
	switch method {
	case "get":
		return urlPrefix + "/" + "healthcheck"
	case "gethealthcheckcfg":
		return urlPrefix + "/" + "healthcheckcfg"
	case "gethealthcheckgpunodescfg":
		return urlPrefix + "/" + "healthcheckgpunodes"
	case "post":
		return urlPrefix + "/" + "healthcheck"
	default:
		logger.Errorf("get health url not support method:%s", method)
		return ""
	}
}

func RdmaHandler(keyswords RdmaWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildRdmaRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s rdma build restful req failed", method)
		return nil, "", false
	}

	switch method {
	case "get", "getrdmatopo":
		logger.Info("get or getrdmatopo from apts")
		return GetRdma(restfulParams)

	case "post":
		logger.Info("post from apts")
		return PostRdma(restfulParams, reqBody)

	case "stop":
		logger.Info("stop from apts")
		return StopRdma(restfulParams, nil)

	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, "", false
	}
}

/* Started by AICoder, pid:91fa366a07484ee4abedf8974899ebc1 */
func GetRdma(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := GetFromWsmToGetStatus(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if err != nil {
		logger.Errorf("get rdma failed body %s error: %v", string(body), err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get rdma failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get rdma json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:91fa366a07484ee4abedf8974899ebc1 */

/* Started by AICoder, pid:a7c84b0bf15c41889037e6bc54174a1a */
func PostRdma(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("post rdma failed body %s error: %v", string(body), err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post rdma failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post rdma json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:a7c84b0bf15c41889037e6bc54174a1a */

/* Started by AICoder, pid:e649fec8d7e84c97b92e42aa13312438 */
func StopRdma(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	_, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("stop rdma failed, error: %v", err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("stop rdma failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	logger.Infof("stop rdma rspCode is:%s", rspCode)

	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}

	return nil, "", isResponseSuc(rspCode, nil, restfulParams)
}

/* Ended by AICoder, pid:e649fec8d7e84c97b92e42aa13312438 */

func buildRdmaRestfulReq(keyswords RdmaWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts/rdma"

	url := getRdmaUrl(method, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getRdmaUrl(method, urlPrefix string) string {
	switch method {
	case "get":
		logger.Infof("get rdma url method:%s", method)
		return urlPrefix + "/" + "querybandwidth"
	case "getrdmatopo":
		return urlPrefix + "/" + "queryrdmatopo"
	case "post":
		return urlPrefix + "/" + "submit"
	case "stop":
		return urlPrefix + "/" + "stop"
	default:
		logger.Errorf("get rdma url not support method:%s", method)
		return ""
	}
}

func VcjobEventsHandler(keyswords VcjobEventsWsmKeywords, reqBody interface{}, method string) ([]map[string]interface{}, string, bool) {
	restfulParams, suc := buildVcjobEventsRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s vcjobevents build restful req failed", method)
		return nil, "", false
	}

	switch method {

	case "get", "getvcjobs":
		logger.Info("get vcjob events from apts")
		return GetVcjobEvents(restfulParams)

	default:
		logger.Errorf("vcjobevents unsupport method:%s", method)
		return nil, "", false
	}
}

/* Started by AICoder, pid:8d6a5d6b4c1149af8bbd9b76273946dc */
func GetVcjobEvents(restfulParams WsmRestfulParams) ([]map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.GetMethod(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if rspCode != 200 && err != nil {
		logger.Errorf("get vcjob events from apts failed body %s error: %v", string(body), err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get vcjob events failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false

	} else if rspCode != 200 {
		logger.Errorf("an internal error occurred in the code")
		return nil, "", false
	} else {
		if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
			return nil, "", false
		}
		if !isResponseSuc(rspCode, nil, restfulParams) {
			return nil, "", false
		}

		var data []map[string]interface{}
		if err = json.Unmarshal(body, &data); err != nil {
			logger.Errorf("get vcjob events json unmarshal body %s error: %v", string(body), err.Error())
			return nil, "", false
		}
		return data, "", true
	}
}

/* Ended by AICoder, pid:8d6a5d6b4c1149af8bbd9b76273946dc */

func VcjobOneEventsHandler(keyswords VcjobEventsWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildVcjobEventsRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s vcjob events build restful req failed", method)
		return nil, "", false
	}

	switch method {

	case "getvcjobevents", "getvcjobcfg":
		logger.Info("get from apts")
		return GetOneVcjobEvents(restfulParams)
	case "post":
		logger.Info("post from apts")
		return PostVcjobCfg(restfulParams, reqBody)

	default:
		logger.Errorf("get vcjobevents or vcjob config unsupport method:%s", method)
		return nil, "", false
	}
}

/* Started by AICoder, pid:d74ab01a5af04714b00c69ffa10bc89b */
func GetOneVcjobEvents(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.GetMethod(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if rspCode != 200 && err != nil {
		logger.Errorf("get one vcjob events from apts failed body %s error: %v", string(body), err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get one vcjob events failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	} else if rspCode != 200 {
		logger.Errorf("an internal error occurred in the code")
		return nil, "", false
	} else {
		if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
			return nil, "", false
		}
		if !isResponseSuc(rspCode, nil, restfulParams) {
			return nil, "", false
		}

		var data map[string]interface{}
		if err = json.Unmarshal(body, &data); err != nil {
			logger.Errorf("get one vcjob events json unmarshal body %s error: %v", string(body), err.Error())
			return nil, "", false
		}
		return data, "", true
	}
}

/* Ended by AICoder, pid:d74ab01a5af04714b00c69ffa10bc89b */

/* Started by AICoder, pid:5eaf63762d4e41a8be90b09cd88eb7af */
func PostVcjobCfg(restfulParams WsmRestfulParams, reqBody []byte) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := restful.PostMethod(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	if err != nil {
		logger.Errorf("post vcjob config from apts failed body %s error: %v", string(body), err.Error())

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post vcjob config failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}
	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post vcjob config json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:5eaf63762d4e41a8be90b09cd88eb7af */

func buildVcjobEventsRestfulReq(keyswords VcjobEventsWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts"

	url := getVcjobEventsUrl(method, urlPrefix, keyswords)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getVcjobEventsUrl(method, urlPrefix string, keyswords VcjobEventsWsmKeywords) string {
	switch method {
	case "get":
		return urlPrefix + "/" + "nsvcjobevents/" + keyswords.ProjectName
	case "getvcjobevents":
		return urlPrefix + "/" + "vcjobevents/" + keyswords.ProjectName + "/" + keyswords.Name
	case "getvcjobs":
		return urlPrefix + "/" + "vcjobs?projectname=" + keyswords.ProjectName
	case "getvcjobcfg":
		return urlPrefix + "/" + "faultanalacfg"
	case "post":
		return urlPrefix + "/" + "faultanalacfg"
	default:
		logger.Errorf("get vcjob events url not support method:%s", method)
		return ""
	}
}

func GetFromWsmToGetData(urlpath string, reqHeaders map[string]string, auth *restful.SSLAuth) ([]byte, map[string][]string, int, error) {
	logger.Info("get from wsm to get data")
	return restful.GetMethod(urlpath, reqHeaders, auth)
}

func AdrmHandler(clusterId string, reqBody interface{}, method string) (map[string]interface{}, bool) {
	restfulParams, suc := buildAdrmRestfulReq(clusterId, method)
	if !suc {
		logger.Error("adrm %s build restful req failed", method)
		return nil, false
	}
	switch method {
	case "get":
		logger.Info("get adrm handler")
		return GetAdrmData(restfulParams)
	case "getnode":
		logger.Info("get node adrm handler")
		return GetNodeData(restfulParams)
	default:
		logger.Errorf("adrm unsupport method:%s", method)
		return nil, false
	}
}

func buildAdrmRestfulReq(clusterId string, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(clusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}
	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}
	urlPrefix := info.MsbURL + "/opapi/wsm/v1/apts"
	logger.Info("urlPrefix is %s", urlPrefix)
	url := getAdrmUrl(method, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getAdrmUrl(method, urlPrefix string) string {
	switch method {
	case "get":
		logger.Info("get adrm url")
		return urlPrefix + "/acceleratordevices"
	case "getnode":
		logger.Info("get node adrm url")
		return urlPrefix + "/gpunodes"
	default:
		logger.Errorf("[buildAdrmRestfulReq] not support method:%s", method)
		return ""
	}
}

func GetAdrmData(restfulParams WsmRestfulParams) (map[string]interface{}, bool) {
	body, _, rspCode, err := GetFromWsmToGetData(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
		return nil, false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, false
	}

	logger.Info("get adrm data")

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get accelerator data json unmarshal error: %v", err.Error())
		return nil, false
	}
	return data, true
}

func GetNodeData(restfulParams WsmRestfulParams) (map[string]interface{}, bool) {
	body, _, rspCode, err := GetFromWsmToGetData(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
		return nil, false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, false
	}

	logger.Info("get node data")

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get node data json unmarshal error: %v", err.Error())
		return nil, false
	}
	return data, true
}

/* Started by AICoder, pid:51718mbb54188441495508ff50e37921fa45d80c */
func InspectHandler(clusterId string, reqBody interface{}, method string, id string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildInspectRestfulReq(clusterId, method, id)
	if !suc {
		logger.Error("wsm %s health build restful req failed", method)
		return nil, "", false
	}

	switch method {
	case "post":
		logger.Info("post inspect handler from apts")
		return PostInspect(restfulParams, reqBody)
	case "stop":
		logger.Info("stop inspect handler from apts")
		return StopInspect(restfulParams, reqBody)
	case "get":
		logger.Info("get inspect handler from apts")
		return GetInspect(restfulParams)
	case "delete", "deleteInspect":
		logger.Info("delete inspect handler from apts")
		return DeleteInspect(restfulParams, reqBody)
	default:
		logger.Errorf("wsm unsupport method:%s", method)
		return nil, "", false
	}
}

/* Ended by AICoder, pid:51718mbb54188441495508ff50e37921fa45d80c */

/* Started by AICoder, pid:14460kd8a6w888114156099de0c9601c4206e5d6 */
func buildInspectRestfulReq(clusterId string, method string, id string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(clusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/wsm/v1" + "/apts"
	url := getInspectUrl(method, urlPrefix, id)
	logger.Info("url:%v", url)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

/* Ended by AICoder, pid:14460kd8a6w888114156099de0c9601c4206e5d6 */

/* Started by AICoder, pid:u5d3b0f160sd4ef14a1c0a4f60d0611d57f5fd3d */
func getInspectUrl(method, urlPrefix string, id string) string {
	switch method {
	case "post":
		return urlPrefix + "/" + "inspectionplan/start"
	case "stop":
		return urlPrefix + "/" + "inspectionplan/stop" + "/" + id
	case "get":
		return urlPrefix + "/" + "inspectionplan/query" + "/" + id
	case "delete":
		return urlPrefix + "/" + "inspectionplan/delete"
	case "deleteInspect":
		return urlPrefix + "/" + "inspectionplan/delete/result"
	default:
		logger.Errorf("get inspect url not support method:%s", method)
		return ""
	}
}

/* Ended by AICoder, pid:u5d3b0f160sd4ef14a1c0a4f60d0611d57f5fd3d */

func PostInspect(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := PostToWsm(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	logger.Infof("post inspect")
	if err != nil {
		logger.Errorf("post inspect failed body %s error: %v,rspCode :%v", string(body), err.Error(), rspCode)
		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("post inspect rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("post inspect statusValues :%s", statusValues)
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "", false
			}
			return nil, statusValues, false
		}
		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("post inspect failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("post inspect json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

func GetInspect(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := GetFromWsmToGetStatus(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	logger.Infof("get inspect")
	if err != nil {
		logger.Errorf("get inspect report failed body %s error: %v", string(body), err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("get inspect rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("get inspect statusValues :%s", statusValues)
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("get inspect report failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "GET", nil, restfulParams) {
		return nil, "", false
	}
	if !isResponseSuc(rspCode, nil, restfulParams) {
		return nil, "", false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get inspect report json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "", false
	}
	return data, "", true
}

func StopInspect(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
	_, _, rspCode, err := PostToWsm(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	logger.Infof("stop inspect")
	if err != nil {
		logger.Errorf("stop inspect failed error: %v", err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("stop inspect rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("stop inspect statusValues :%s", statusValues)
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("stop inspect failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}

	return nil, "", isResponseSuc(rspCode, nil, restfulParams)
}

func DeleteInspect(restfulParams WsmRestfulParams, reqBody interface{}) (map[string]interface{}, string, bool) {
	_, _, rspCode, err := PostToWsm(restfulParams.URL, restfulParams.Header, reqBody, restfulParams.SSL)
	logger.Infof("delete inspect")

	if rspCode == http.StatusNotFound {
		return nil, "", true
	}

	if err != nil {
		logger.Errorf("delete inspect failed error: %v", err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("delete inspect rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("delete inspect statusValues :%s", statusValues)
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				logger.Errorf("stop inspect failed error: %s", msg)
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	if !isRestfuleMethodSuc(err, "POST", nil, restfulParams) {
		return nil, "", false
	}

	return nil, "", isResponseSuc(rspCode, nil, restfulParams)
}

func SwrHandler(keyswords SwrWsmKeywords, reqBody []byte, method string) (map[string]interface{}, string, bool) {
	restfulParams, suc := buildSwrRestfulReq(keyswords, method)
	if !suc {
		logger.Error("wsm %s swr build restful req failed", method)
		return nil, "swr build restful req failed", false
	}

	switch method {

	case "get":
		logger.Info("get from apts")
		return GetSwr(restfulParams)

	default:
		return nil, "swr handler failed", false
	}
}

/* Started by AICoder, pid:9e7510eaa9744b069aed42a0a7ee063d */
func GetSwr(restfulParams WsmRestfulParams) (map[string]interface{}, string, bool) {
	body, _, rspCode, err := GetFromWsmToGetStatus(restfulParams.URL, restfulParams.Header, restfulParams.SSL)
	logger.Info("get swr failed body %s rspCode:%d error: %v", string(body), rspCode)
	if err != nil {
		logger.Errorf("get swr failed body %s rspCode:%d", string(body), rspCode, err.Error())

		if rspCode != http.StatusOK && rspCode != http.StatusBadRequest {
			logger.Infof("get swr rspCode :%v", rspCode)
			statusValues, err := GetCodeStatusValues(rspCode)
			if err != nil {
				logger.Errorf("get swr statusValues :%s", statusValues)
				logger.Errorf("get code status values failed rspCode is:%d", rspCode)
				return nil, "get code status values or swr statusValues failed", false
			}
			return nil, statusValues, false
		}

		var errorMsg map[string]string
		if jsonErr := json.Unmarshal([]byte(err.Error()), &errorMsg); jsonErr == nil {
			if msg, exists := errorMsg["err"]; exists {
				return nil, msg, false
			}
		}
		return nil, err.Error(), false
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		logger.Errorf("get swr json unmarshal body %s error: %v", string(body), err.Error())
		return nil, "get swr json unmarshal failed", false
	}
	return data, "", true
}

/* Ended by AICoder, pid:9e7510eaa9744b069aed42a0a7ee063d */

func buildSwrRestfulReq(keyswords SwrWsmKeywords, method string) (WsmRestfulParams, bool) {
	info, err := GetOpenpalette(keyswords.ClusterId)
	if err != nil {
		return WsmRestfulParams{}, false
	}

	header := restful.DefaultHeaders()
	if len(info.Token) > 0 {
		header["X-Auth-Token"] = info.Token
	}

	urlPrefix := info.MsbURL + "/opapi/swr/v1" + "/tenants"

	url := getSwrUrl(method, urlPrefix)
	return WsmRestfulParams{URL: url, Header: header, SSL: info.SSL}, true
}

func getSwrUrl(method, urlPrefix string) string {
	switch method {
	case "get":
		return urlPrefix + "/" + "admin" + "/" + "images"
	default:
		logger.Errorf("get swr url not support method:%s", method)
		return "get swr url failed"
	}
}

/* Started by AICoder, pid:d15c568386v9aa114b97090c905bb71e36d4e694 */
func GetCodeStatusValues(rspCode int) (string, error) {
	logger.Infof("get code status values failed rspCode is:%d", rspCode)
	switch rspCode {
	case http.StatusNotFound:
		logger.Errorf("Not Found: The requested resource could not be found on the server.")
		return " Not Found: The requested resource could not be found on the server.", nil
	case http.StatusUnauthorized:
		logger.Errorf("Unauthorized: The client has not provided valid authentication credentials, and access to the resource is denied.")
		return " Unauthorized: The client has not provided valid authentication credentials, and access to the resource is denied.", nil
	case http.StatusForbidden:
		logger.Errorf("Forbidden: The client is forbidden from accessing the requested resource.")
		return " Forbidden: The client is forbidden from accessing the requested resource.", nil
	case http.StatusBadGateway:
		logger.Errorf("Bad Gateway: The server, while acting as a gateway or proxy, received an invalid response from an upstream server it accessed in attempting to fulfill the request.")
		return " Bad Gateway: The server, while acting as a gateway or proxy, received an invalid response from an upstream server it accessed in attempting to fulfill the request.", nil
	case http.StatusNotImplemented:
		logger.Errorf("Not Implemented: The server does not support the functionality required to fulfill the request.")
		return " Not Implemented: The server does not support the functionality required to fulfill the request.", nil
	case http.StatusGatewayTimeout:
		logger.Errorf("Gateway Timeout: The server, acting as a gateway or proxy, did not receive a timely response from an upstream server.")
		return " Gateway Timeout: The server, acting as a gateway or proxy, did not receive a timely response from an upstream server.", nil
	case http.StatusHTTPVersionNotSupported:
		logger.Errorf("HTTP Version Not Supported: The server does not support the HTTP protocol version used in the request.")
		return " HTTP Version Not Supported: The server does not support the HTTP protocol version used in the request.", nil
	case http.StatusMethodNotAllowed:
		logger.Errorf("Method Not Allowed: The request method is not supported for the requested resource.")
		return " Method Not Allowed: The request method is not supported for the requested resource.", nil
	default:
		logger.Errorf("get code status failed rspCode is:%d", rspCode)
		return "", fmt.Errorf("get code status failed rspCode is:%d", rspCode)
	}
}

/* Ended by AICoder, pid:d15c568386v9aa114b97090c905bb71e36d4e694 */
