package job

import (
	"cwsm/tools/commontools/infa/task"
	"cwsm/tools/commontools/infa/util"
	"cwsm/tools/commontools/logger"
	"cwsm/tools/commontools/storage/rediz"
	"fmt"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

type fakeOperateResourceJobHandler struct {
	op string
}

func (c *fakeOperateResourceJobHandler) DoOperation(logId string, request interface{}) (*GenericDoOperationRsp, error) {
	logger.Infof("[DoOperation] [logId %s] do operation finished", logId)
	if strings.HasPrefix(request.(string), "fail") {
		return nil, fmt.Errorf("DoOperation failed")
	}
	return &GenericDoOperationRsp{InnerJobId: util.UUID(), ResourceId: "", ServiceName: "vrm"}, nil
}
func (c *fakeOperateResourceJobHandler) GetExpectedJobStates() []string {
	return []string{JobState_Finished}
}
func (c *fakeOperateResourceJobHandler) QueryInnerJob(logId string, jobId string, serviceName string) (*GenericJobRsp, error) {
	logger.Infof("[QueryInnerJob] [logId %s] get inner job(%s) info finished", logId, jobId)
	return &GenericJobRsp{JobId: jobId, JobState: JobState_Finished, InstanceId: util.UUID()}, nil
}
func (c *fakeOperateResourceJobHandler) GetExpectedResourceStates() []string {
	if c.op == "delete" {
		return []string{ResourceState_NotFound}
	}
	return []string{ResourceState_Available}
}
func (c *fakeOperateResourceJobHandler) QueryResource(logId string, id string) (*GenericResourceRsp, error) {
	logger.Infof("[QueryResource] [logId %s] get resource(%s) state finished", logId, id)
	if strings.HasPrefix(id, "fail") {
		return nil, fmt.Errorf("QueryResource failed")
	}
	return &GenericResourceRsp{ResourceId: id, ResourceName: "", ResourceState: ResourceState_Available}, nil
}

type fakeOperateResourceJobPostHandler struct{}

func (c *fakeOperateResourceJobPostHandler) OnFail(logId string, shareParam *ResourceJobGlobalContext) error {
	logger.Infof("[PostHandler] do on fail, args: %s", util.ToJSONStr(shareParam))
	return nil
}
func (c *fakeOperateResourceJobPostHandler) OnSuccess(logId string, shareParam *ResourceJobGlobalContext) error {
	logger.Infof("[PostHandler] do on success, args: %s", util.ToJSONStr(shareParam))
	return nil
}
func TestVolumeService(t *testing.T) {
	rediz.UseMemClient()
	defer rediz.FlushAll()

	RegisterFailHandler(Fail)
	RunSpecs(t, "ResourceJob Suite")
}

var _ = Describe("ResourceJob", func() {
	var UtPatches *gomonkey.Patches
	BeforeEach(func() {
		UtPatches = gomonkey.ApplyGlobalVar(&UT, true)
	})
	AfterEach(func() {
		UtPatches.Reset()
	})
	It("should ok", func() {
		resourceSyncJob := NewResourceSyncJob(&Object{Name: "vm01"}, "request", &fakeOperateResourceJobHandler{}).
			RegisterMonitorJob(&fakeOperateResourceJobHandler{}).RegisterMonitorResource(&fakeOperateResourceJobHandler{}).
			RegisterPostHandler(&fakeOperateResourceJobPostHandler{})
		resourceAsyncJob := NewResourceAsyncJob("createVm").Add(resourceSyncJob)
		async := resourceAsyncJob.BuildRunnerAsync()
		err := async.Start(true)
		Expect(err).To(BeNil())
		Expect(async.Status).To(Equal(task.AsyncFinish))
		ids := resourceAsyncJob.CollectFinalSuccessResources()
		Expect(len(ids) == 1).To(BeTrue())
	})
	It("should ok when batch operate", func() {
		resourceAsyncJob := NewResourceAsyncJob("createVm")
		for i := 0; i < 3; i++ {
			resourceSyncJob := NewResourceSyncJob(&Object{Name: fmt.Sprintf("vm%d", i)}, "request", &fakeOperateResourceJobHandler{}).
				RegisterMonitorJob(&fakeOperateResourceJobHandler{}).RegisterMonitorResource(&fakeOperateResourceJobHandler{}).
				RegisterPostHandler(&fakeOperateResourceJobPostHandler{})
			resourceAsyncJob.Add(resourceSyncJob)
		}
		async := resourceAsyncJob.BuildRunnerAsync()
		err := async.Start(true)
		Expect(err).To(BeNil())
		Expect(async.Status).To(Equal(task.AsyncFinish))
		ids := resourceAsyncJob.CollectFinalSuccessResources()
		Expect(len(ids) == 3).To(BeTrue())
	})
	It("should partial fail when batch operate", func() {
		resourceAsyncJob := NewResourceAsyncJob("createVm")
		for i := 0; i < 2; i++ {
			resourceSyncJob := NewResourceSyncJob(&Object{Name: fmt.Sprintf("vm%d", i)}, "request", &fakeOperateResourceJobHandler{}).
				RegisterMonitorJob(&fakeOperateResourceJobHandler{}).RegisterMonitorResource(&fakeOperateResourceJobHandler{}).
				RegisterPostHandler(&fakeOperateResourceJobPostHandler{})
			resourceAsyncJob.Add(resourceSyncJob)
		}
		resourceAsyncJob.Add(NewResourceSyncJob(&Object{Name: "fail_vm"}, "fail_request", &fakeOperateResourceJobHandler{}).
			RegisterMonitorJob(&fakeOperateResourceJobHandler{}).RegisterMonitorResource(&fakeOperateResourceJobHandler{}).
			RegisterPostHandler(&fakeOperateResourceJobPostHandler{}))
		async := resourceAsyncJob.BuildRunnerAsync()
		err := async.Start(true)
		Expect(strings.Contains(err.Error(), "DoOperation failed")).To(BeTrue())
		Expect(async.Status).To(Equal(task.AsyncError))
		ids := resourceAsyncJob.CollectFinalSuccessResources()
		Expect(len(ids) == 2).To(BeTrue())
	})
	It("should fail when query resource fail", func() {
		resourceSyncJob := NewResourceSyncJob(&Object{Id: "fail_update_id", Name: "update_vm"}, "request", &fakeOperateResourceJobHandler{}).
			RegisterMonitorJob(&fakeOperateResourceJobHandler{}).RegisterMonitorResource(&fakeOperateResourceJobHandler{}).
			RegisterPostHandler(&fakeOperateResourceJobPostHandler{})
		resourceAsyncJob := NewResourceAsyncJob("updateVm").Add(resourceSyncJob)
		async := resourceAsyncJob.BuildRunnerAsync()
		err := async.Start(true)
		Expect(strings.Contains(err.Error(), "QueryResource failed")).To(BeTrue())
		Expect(async.Status).To(Equal(task.AsyncError))
		ids := resourceAsyncJob.CollectFinalSuccessResources()
		Expect(len(ids) == 0).To(BeTrue())
	})
	It("should timeout when resource not be deleted", func() {
		resourceSyncJob := NewResourceSyncJob(&Object{Id: util.UUID(), Name: "delete_vm"}, "request", &fakeOperateResourceJobHandler{}).
			RegisterMonitorJob(&fakeOperateResourceJobHandler{}).RegisterMonitorResource(&fakeOperateResourceJobHandler{op: "delete"}).
			RegisterPostHandler(&fakeOperateResourceJobPostHandler{})
		resourceAsyncJob := NewResourceAsyncJob("deleteVm").Add(resourceSyncJob)
		async := resourceAsyncJob.BuildRunnerAsync()
		err := async.Start(true)
		Expect(strings.Contains(err.Error(), "ended with timeout")).To(BeTrue())
		Expect(async.Status).To(Equal(task.AsyncError))
		ids := resourceAsyncJob.CollectFinalSuccessResources()
		Expect(len(ids) == 0).To(BeTrue())
	})
})
