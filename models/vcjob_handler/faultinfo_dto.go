package vcjob_handler

/* Started by AICoder, pid:508f1qa3e0md06b148c108bce0e71630efc94b27 */
const (
	Layout                    = "2006-01-02T15:04:05.999Z"
	ECC_ERROR_Desc_zh         = "GPU 内存2比特ECC错误。"
	ECC_ERROR_Desc_en         = "2-bit ECC GPU memory errors."
	GpuPciLost_Desc_zh        = "GPU设备掉卡。"
	GpuPciLost_Desc_en        = "GPU Device Removed From the Node."
	ProcessEngineHang_Desc_zh = "GPU COMMAND PROCESSOR 引擎 hang。"
	ProcessEngineHang_Desc_en = "GPU COMMAND PROCESSOR engine hang."
	ComputeEngineHang_Desc_zh = "GPU 计算引擎hang。"
	ComputeEngineHang_Desc_en = "GPU compute engine hang."
	DMAEngineHang_Desc_zh     = "GPU DMA 引擎 hang。"
	DMAEngineHang_Desc_en     = "GPU DMA engine hang."
	GPUQueueHang_Desc_zh      = "GPU 队列 hang。"
	GPUQueueHang_Desc_en      = "GPU queue hang."

	SMMUFault_Desc_zh                  = "GPU系统内存管理单元故障。"
	SMMUFault_Desc_en                  = "GPU System Memory Management Unit fault."
	VideoEngineHang_Desc_zh            = "GPU video 引擎 hang。"
	VideoEngineHang_Desc_en            = "GPU video engine hang."
	BusError_Desc_zh                   = "GPU总线错误。"
	BusError_Desc_en                   = "GPU Bus Error."
	HeartBeatLost_Desc_zh              = "GPU设备与host心跳丢失。"
	HeartBeatLost_Desc_en              = "GPU device lost heartbeat with host."
	KernelOOM_Desc_zh                  = "GPU节点操作系统内核OOM故障，可能导致系统内存分配失败。"
	KernelOOM_Desc_en                  = "GPU node operating system kernel OOM failure, Maybe system memory allocation failed."
	DeviceBoot_Desc_zh                 = "GPU设备加载驱动失败。"
	DeviceBoot_Desc_en                 = "GPU device driver failed to load."
	KernelPanic_Desc_zh                = "操作系统内核panic。"
	KernelPanic_Desc_en                = "operating system kernel panic."
	FirmwareFailure_Desc_zh            = "GPU固件异常故障。"
	FirmwareFailure_Desc_en            = "GPU firmware anomaly failure."
	GpuResidual_Desc_zh                = "GPU占用残留。"
	GpuResidual_Desc_en                = "residual GPU utilization."
	PCleAERError_Desc_zh               = "GPU PCIe AER 故障。"
	PCleAERError_Desc_en               = "GPU PCIe AER failure."
	GPUUnvailable_Desc_zh              = "GPU驱动掉卡。"
	GPUUnvailable_Desc_en              = "GPU unavailable failure."
	RDMADeviceDroped_Desc_zh           = "RDMA设备掉卡。"
	RDMADeviceDroped_Desc_en           = "RDMA Device Removed From the Node."
	LinkFailure_Desc_zh                = "RDMA链路断。"
	LinkFailure_Desc_en                = "RDMA link down."
	PodInterfaceDown_Desc_zh           = "POD RDMA网口状态异常。"
	PodInterfaceDown_Desc_en           = "RDMA port status abnormal in POD."
	NodeInterfaceDown_Desc_zh          = "节点RDMA网卡状态错误。"
	NodeInterfaceDown_Desc_en          = "RDMA devices status error in node."
	InsufficientClusterRes_Desc_zh     = "集群资源不足。"
	InsufficientClusterRes_Desc_en     = "insufficient cluster resources."
	AffinityIssue_Desc_zh              = "节点亲和性标签问题。"
	AffinityIssue_Desc_en              = "node affinity label issue."
	GPUResourceInsufficient_Desc_zh    = "节点GPU资源不足。"
	GPUResourceInsufficient_Desc_en    = "insufficient GPU resources on the node."
	RDMAResourceInsufficient_Desc_zh   = "节点RDMA资源不足。"
	RDMAResourceInsufficient_Desc_en   = "insufficient RDMA resources on the node."
	CPUResoureceInsufficient_Desc_zh   = "节点CPU资源不足。"
	CPUResoureceInsufficient_Desc_en   = "insufficient CPU resources on the node."
	MemoryResourceInsufficient_Desc_zh = "节点内存资源不足。"
	MemoryResourceInsufficient_Desc_en = "insufficient Memory resources on the node."
	GpuMemoryUnitInvalid_Desc_zh       = "GPU设备CR内存单位错误。"
	GpuMemoryUnitInvalid_Desc_en       = "incorrect CR(GPU device Custom Resource) memory unit."
	GpuCRCountAbnormal_Desc_zh         = "GPU设备CR数量异常。"
	GpuCRCountAbnormal_Desc_en         = "abnormal CR(GPU device Custom Resource) count."

	PodFailedRdmaAlreadyAllocated_Desc_zh = "Pod在节点启动失败-RDMA已分配。"
	PodFailedRdmaAlreadyAllocated_Desc_en = "pod failed to start on node - RDMA already allocated."
	GpuUuuidDuplicate_Desc_zh             = "GPU UUID 重复。"
	GpuUuuidDuplicate_Desc_en             = "duplicate GPU UUID."
	GpuDriverFailure_Desc_zh              = "GPU驱动失败。"
	GpuDriverFailure_Desc_en              = "GPU driver failure."
	GpuTopoError_Desc_zh                  = "GPU拓扑错误。"
	GpuTopoError_Desc_en                  = "GPU topology error."

	QuotaUnspecified_Desc_zh    = "用户蓝图未指定limits配额资源。"
	QuotaUnspecified_Desc_en    = "user blueprint does not specify resource quota limits."
	QuotaExceeded_Desc_zh       = "用户蓝图资源请求超出租户配额。"
	QuotaExceeded_Desc_en       = "resource requests in the user blueprint exceed the tenant's quota."
	NodeNotReady_Desc_zh        = "节点状态not ready。"
	NodeNotReady_Desc_en        = "Node status not ready."
	PullImageFailed_Desc_zh     = "节点拉取镜像失败。"
	PullImageFailed_Desc_en     = "SWR failed to pull image on node."
	InvalidCommand_Desc_zh      = "用户蓝图command错误。"
	InvalidCommand_Desc_en      = "invalid command in user blueprint."
	MultusCniNetError_Desc_zh   = "multus网络组件逻辑处理异常。"
	MultusCniNetError_Desc_en   = "Multus network component logic processing exception."
	RoceStorageAbnormal_Desc_zh = "挂载卷时roce存储网络异常。"
	RoceStorageAbnormal_Desc_en = "RoCE storage network exception during volume mount."
	SpaceNotEnough_Desc_zh      = "节点空间不足。"
	SpaceNotEnough_Desc_en      = "worker node disk space critical."

	Xid2KernelPanic       = "2:0x64020001 operating system kernel panic"
	Xid3KernelOOM         = "3:0x62021001 operating system kernel OOM"
	Xid4DeviceBoot        = "4:0xA4030001 device boot failure"
	Xid5ProcessEngineHang = "5:0xA4031001 COMMAND PROCESSOR engine hang"
	Xid6DMAEngineHang     = "6:0xA4031002 DMA engine hang"
	Xid7ComputeEngineHang = "7:0xA4031003 compute engine hang"
	Xid8VideoEngineHang   = "8:0xA4031004 video engine hang"
	Xid9SMMUFault         = "9:0xA3032001 SMMU fault"
	Xid11ECCError         = "11:0xA4033002 2-bit ECC error"
	Xid13PCleAERError     = "13:0xA4034001 AER error"
	Xid15FirmwareFailure  = "15:0xA4040001 firmware error"
	Xid16HeartBeatLost    = "16:0xA4041001 heartbeat error"
	Xid17BusError         = "17:0xA4031005 bus error"
	Xid18GPUQueueHang     = "18:0xA4031006 queue hang"
	GpuPciLost            = "GpuPciLost"
	GPUUnvailable         = "Unavailable"
	GpuResidual           = "GpuResidual"

	RDMADeviceDroped  = "RDMADeviceDropped"
	LinkFailure       = "LinkFailure"
	PodInterfaceDown  = "PodInterfaceDown"
	NodeInterfaceDown = "NodeInterfaceDown"

	InsufficientClusterRes        = "Insufficient cluster resources"
	AffinityIssue                 = "Blueprint and node labels do not match"
	GPUResourceInsufficient       = "Insufficient GPU Resources"
	RDMAResourceInsufficient      = "Insufficient RDMA Resources"
	CPUResoureceInsufficient      = "Insufficient CPU Resources"
	MemoryResourceInsufficient    = "Insufficient Memory Resources"
	GpuMemoryUnitInvalid          = "Gpu memory unit invalid"
	GpuCRCountAbnormal            = "Gpu CR num invalid"
	PodFailedRdmaAlreadyAllocated = "RDMANetwork vf is already allocated"
	GpuUuuidDuplicate             = "GpuUuidDuplicate"
	GpuDriverFailure              = "Gpu driver not ready"
	NodeNotReady                  = "Node not ready"
	QuotaUnspecified              = "Failed quota: must specify resourcequota resource for job"
	QuotaExceeded                 = "Exceeded quota"

	PullImageFailed      = "Pull image error"
	CreateShimTaskFailed = "Failed to create shim task: Others"
	MultusCniNetError    = "Multus cni network error"
	NfsServerError       = "parse nfs server error"
	SpaceNotEnough       = "Insufficient disk space"
)

/* Ended by AICoder, pid:508f1qa3e0md06b148c108bce0e71630efc94b27 */

type FaultItem struct {
	VcjobId   string `json:"vcjobId"`
	Id        string `json:"id"`
	FaultCode int64  `json:"faultCode"`
	FaultInfo string `json:"faultInfo"`
}

type FaultInfo struct {
	FaultId           string              `json:"fault_id"`
	FaultCode         int64               `json:"fault_code"`
	RaisedTime        string              `json:"raised_time"`
	ClearedTime       string              `json:"cleared_time"`
	ReportedTime      string              `json:"reported_time"`
	DescriptionZH     string              `json:"description_zh"`
	DescriptionEN     string              `json:"description_en"`
	EnvId             string              `json:"env_id"`
	EnvName           string              `json:"env_name"`
	RootCauseObject   []*RootCauseObject  `json:"root_cause_objects"`
	RootCauseDetail   []*RootCauseDetail  `json:"root_cause_detail"`
	AssociatedObjects []*AssociatedObject `json:"associated_objects"`
	NeedReportToNorth bool                `json:"need_report_to_north"`
}

type VcjobCloudClusterInfo struct {
	UUID        string `json:"uuid"`
	Name        string `json:"name"`
	TenantName  string `json:"tenantName"`
	ClusterName string `json:"clusterName"`
	ClusterId   string `json:"clusterId"`
	CloudId     string `json:"cloudId"`
	CloudName   string `json:"cloudName"`
}

type RootCauseObject struct {
	EnvId string `json:"env_id"`
	Id    string `json:"id"`
	Name  string `json:"name"`
	Moc   string `json:"moc"`
}

type RootCauseDetail struct {
	RcObjectId string   `json:"rc_object_id"`
	Type       string   `json:"type"`
	Data       []string `json:"data"`
}

type AssociatedObject struct {
	EnvId string `json:"env_id"`
	Id    string `json:"id"`
	Name  string `json:"name"`
	Moc   string `json:"moc"`
}

type FaultInfoWsm struct {
	Name         string              `json:"name"`
	NameSpace    string              `json:"namespace"`
	UID          string              `json:"uid,omitempty"`
	ReportTime   string              `json:"reportTime,omitempty"`
	GpuTrainInfo []ResourceEventInfo `json:"gpuTrainInfo,omitempty"`
	// GpuTrainInfo []ResourceEventInfo `json:"gpuWarningInfo,omitempty"`
	JobWarningInfo  []ResourceEventInfo `json:"jobWarningInfo,omitempty"`
	RdmaWarningInfo []ResourceEventInfo `json:"rdmaDeviceWarningInfo,omitempty"`
}

type ResourceEventInfo struct {
	TimeStamp  string `json:"timeStamp"`
	JobName    string `json:"jobName,omitempty"`
	ObjectType string `json:"objectType"`
	Object     string `json:"object"`
	ObjectId   string `json:"objectId"`
	NodeName   string `json:"nodeName"`
	NodeId     string `json:"nodeId,omitempty"`
	Reason     string `json:"reason"`
	Message    string `json:"message"`
	EventName  string `json:"eventName,omitempty"`
	Suggestion string `json:"suggestion,omitempty"`
}
type FaultCodeInfo struct {
	FaultCode     int64  `json:"faultCode"`
	DescriptionZH string `json:"description_zh"`
	DescriptionEN string `json:"description_en"`
	RootCauseMoc  string `json:"route_cause_moc"`
}

/* Started by AICoder, pid:j8a9etb5c7k2d3714b150934008c802a08b44c29 */
var ReasonToFaultCode = map[string]*FaultCodeInfo{
	Xid11ECCError:         {31017001, ECC_ERROR_Desc_zh, ECC_ERROR_Desc_en, ""},
	GpuPciLost:            {31017002, GpuPciLost_Desc_zh, GpuPciLost_Desc_en, ""},
	Xid5ProcessEngineHang: {31017003, ProcessEngineHang_Desc_zh, ProcessEngineHang_Desc_en, ""},
	Xid7ComputeEngineHang: {31017004, ComputeEngineHang_Desc_zh, ComputeEngineHang_Desc_en, ""},
	Xid6DMAEngineHang:     {31017005, DMAEngineHang_Desc_zh, DMAEngineHang_Desc_en, ""},
	Xid8VideoEngineHang:   {31017006, VideoEngineHang_Desc_zh, VideoEngineHang_Desc_en, ""},
	Xid17BusError:         {31017007, BusError_Desc_zh, BusError_Desc_en, ""},
	Xid16HeartBeatLost:    {31017008, HeartBeatLost_Desc_zh, HeartBeatLost_Desc_en, ""},
	Xid3KernelOOM:         {31017009, KernelOOM_Desc_zh, KernelOOM_Desc_en, ""},
	Xid4DeviceBoot:        {31017010, DeviceBoot_Desc_zh, DeviceBoot_Desc_en, ""},
	Xid2KernelPanic:       {31017011, KernelPanic_Desc_zh, KernelPanic_Desc_en, ""},
	Xid15FirmwareFailure:  {31017012, FirmwareFailure_Desc_zh, FirmwareFailure_Desc_en, ""},
	GpuResidual:           {31017013, GpuResidual_Desc_zh, GpuResidual_Desc_en, ""},
	Xid13PCleAERError:     {31017014, PCleAERError_Desc_zh, PCleAERError_Desc_en, ""},
	GPUUnvailable:         {31017015, GPUUnvailable_Desc_zh, GPUUnvailable_Desc_en, ""},
	Xid9SMMUFault:         {31017016, SMMUFault_Desc_zh, SMMUFault_Desc_en, ""},
	Xid18GPUQueueHang:     {31017018, GPUQueueHang_Desc_zh, GPUQueueHang_Desc_en, ""},

	RDMADeviceDroped:  {33012001, RDMADeviceDroped_Desc_zh, RDMADeviceDroped_Desc_en, ""},
	LinkFailure:       {33012002, LinkFailure_Desc_zh, LinkFailure_Desc_en, ""},
	PodInterfaceDown:  {33012003, PodInterfaceDown_Desc_zh, PodInterfaceDown_Desc_en, ""},
	NodeInterfaceDown: {33012101, NodeInterfaceDown_Desc_zh, NodeInterfaceDown_Desc_en, ""},
	// "RDMA device status unhealthy":               {33012002, "RDMA设备状态不健康", "RDMA device status unhealthy", ""},
	InsufficientClusterRes:        {40003001, InsufficientClusterRes_Desc_zh, InsufficientClusterRes_Desc_en, ""},
	AffinityIssue:                 {40003002, AffinityIssue_Desc_zh, AffinityIssue_Desc_en, ""}, // 修改并取消注释
	GPUResourceInsufficient:       {40003003, GPUResourceInsufficient_Desc_zh, GPUResourceInsufficient_Desc_en, ""},
	RDMAResourceInsufficient:      {40003004, RDMAResourceInsufficient_Desc_zh, RDMAResourceInsufficient_Desc_en, ""},
	CPUResoureceInsufficient:      {40003005, CPUResoureceInsufficient_Desc_zh, CPUResoureceInsufficient_Desc_en, ""},
	MemoryResourceInsufficient:    {40003006, MemoryResourceInsufficient_Desc_zh, MemoryResourceInsufficient_Desc_en, ""},
	GpuMemoryUnitInvalid:          {40003007, GpuMemoryUnitInvalid_Desc_zh, GpuMemoryUnitInvalid_Desc_en, ""},
	GpuCRCountAbnormal:            {40003008, GpuCRCountAbnormal_Desc_zh, GpuCRCountAbnormal_Desc_en, ""},
	PodFailedRdmaAlreadyAllocated: {40003009, PodFailedRdmaAlreadyAllocated_Desc_zh, PodFailedRdmaAlreadyAllocated_Desc_en, ""},
	GpuUuuidDuplicate:             {40003010, GpuUuuidDuplicate_Desc_zh, GpuUuuidDuplicate_Desc_en, ""},
	GpuDriverFailure:              {40003011, GpuDriverFailure_Desc_zh, GpuDriverFailure_Desc_en, ""},
	// "Gpu topo invalid":                       {40003012, GpuTopoError_Desc_zh, GpuTopoError_Desc_en, ""},
	QuotaUnspecified:     {40003012, QuotaUnspecified_Desc_zh, QuotaUnspecified_Desc_en, ""},
	QuotaExceeded:        {40003013, QuotaExceeded_Desc_zh, QuotaExceeded_Desc_en, ""},
	NodeNotReady:         {40003014, NodeNotReady_Desc_zh, NodeNotReady_Desc_en, ""},
	PullImageFailed:      {40003015, PullImageFailed_Desc_zh, PullImageFailed_Desc_en, ""},
	CreateShimTaskFailed: {40003016, InvalidCommand_Desc_zh, InvalidCommand_Desc_en, ""},
	MultusCniNetError:    {40003017, MultusCniNetError_Desc_zh, MultusCniNetError_Desc_en, ""},
	NfsServerError:       {40003018, RoceStorageAbnormal_Desc_zh, RoceStorageAbnormal_Desc_en, ""},
	SpaceNotEnough:       {40003019, SpaceNotEnough_Desc_zh, SpaceNotEnough_Desc_en, ""},
}

/* Ended by AICoder, pid:j8a9etb5c7k2d3714b150934008c802a08b44c29 */
var ReasonFixed = map[int64]struct{}{
	31017001: {}, //"11:0xA4033002 2-bit ECC error"
	31017002: {}, //"GpuPciLost"
	31017003: {}, //"5:0xA4031001 COMMAND PROCESSOR engine hang"
	31017004: {}, //"7:0xA4031003 compute engine hang"
	31017005: {}, //"6:0xA4031002 DMA engine hang"
	31017006: {}, //"8:0xA4031004 video engine hang"
	31017007: {}, //"17:0xA4031005 bus error"
	31017008: {}, //"16:0xA4041001 heartbeat error"
	31017009: {}, //"3:0x62021001 operating system kernel OOM"
	31017010: {}, //"4:0xA4030001 device boot failure"
	31017011: {}, //"2:0x64020001 operating system kernel panic"
	31017012: {}, //"15:0xA4040001 firmware error"
	31017014: {}, //"13:0xA4034001 AER error"
	31017016: {}, //"9:0xA3032001 SMMU fault"
}

type RdmaDetailInfo struct {
	NodeId     string
	NodeName   string
	Detail     string
	RaisedTime string
}
