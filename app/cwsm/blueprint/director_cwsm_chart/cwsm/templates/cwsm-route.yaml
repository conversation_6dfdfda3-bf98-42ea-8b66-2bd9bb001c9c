apiVersion: {{ template "oki.msb.apiVersion" . }}
kind: MsbRoute
spec:
  routeClassName: router
  httpRules:
  - match:
      path: /api/v1.0/cwsm
      protocol: REST
      rewriteTarget: /api/v1.0/cwsm
    backend:
      service:
        name: serviceslice-{{ .Values.serviceName.cwsm }}
        portName: httpPort
    advancedConfig:
      lbPolicy: round-robin
metadata:
  name: route-{{ .Values.serviceName.cwsm }}
  namespace: {{ .Values.global.OPENPALETTE_NAMESPACE }}