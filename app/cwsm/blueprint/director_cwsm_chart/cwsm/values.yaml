# 云原生应用 镜像规范
repository: swr:2512
tenant: ranoss
name: cwsm
version: appVersion_will_replaced
pullPolicy: Always

# 微服务自己的部署参数定义在这里，再在spd中暴露出去
namespace: ranoss
cwsm_cwsm_replicas: 1
logpath_type: hostPath
logpath_key: path
logpath_value: /var/zte-log

zenap_elasticsearch_openpalette_secret_name: 'cwsm'

cwsm_init_cpu_requests: '1'
cwsm_init_memory_requests: '100Mi'
cwsm_init_cpu_limits: '1'
cwsm_init_memory_limits: '200Mi'

language: 'EN'
logo: 0
version_no: ''
product_version_no: ''
enable_ssl: "false"
scheme: "HTTP"
openstackNativeInterface: 'true'
deploymode: 'director'
scene: '2'
business_network_ip_stack: 'double'
mq_host: 'rabbitmq-director'
gateway_host: 'inner-router-ranoss'
original_interface_protocol: 'https'
cassandra_host: 'cassandra1-director,cassandra2-director,cassandra3-director'
es_host: 'es1-director,es2-director,es3-director'
zk_host: 'zookeeper-director'
etcd_host: 'etcd-director'
north_external_vip: ''
south_external_vip: ''
node_nums_for_kafka: '3'
director_scale_mode: 'small'
north_external_vip_ipv6: ''
south_external_vip_ipv6: ''
kafka_host: ''
kafka_ssl_host: ''
cwsm_cwsm_cpu_request: '0.01'
cwsm_cwsm_mem_request: '64Mi'
cwsm_cwsm_cpu_limit: '24'
cwsm_cwsm_mem_limit: '3Gi'


# 暴露给spd获取的公共服务参数
redis_director_openpalette_secret_name: ""
cwsm_db_openpalette_secret_name: ""
CERT_CENTER_SECRET_NAME: ""
image_db_openpalette_secret_name: ""
OPENPALETTE_REDIS_ADDRESS: ""
OPENPALETTE_REDIS_PORT: ""
OPENPALETTE_REDIS_PASSWORD: ""
OPENPALETTE_REDIS_SENTINEL_ADDRESS: ""
OPENPALETTE_REDIS_SENTINEL_PORT: ""
OPENPALETTE_REDIS_SENTINEL_MASTERNAME: ""
OPENPALETTE_KAFKA_ADDRESS: ""
OPENPALETTE_KAFKA_ZOOKEEPER_ADDRESS: ""
OPENPALETTE_KAFKA_PORT: ""
OPENPALETTE_KAFKA_ZOOKEEPER_PORT: ""
# 公共服务参数,get_property获取：
OPENPALETTE_ELASTICSEARCH_ADDRESS: ""
OPENPALETTE_ELASTICSEARCH_HTTP_PORT: ""
OPENPALETTE_ELASTICSEARCH_TCP_PORT: ""
OPENPALETTE_ELASTICSEARCH_CLUSTER_NAME: ""
OPENPALETTE_ELASTICSEARCH_SECURITY_REINFORCE: ""
OPENPALETTE_ELASTICSEARCH_USERNAME: ""
OPENPALETTE_ELASTICSEARCH_PASSWORD: ""
OPENPALETTE_PG_ADDRESS: ""
OPENPALETTE_PG_PORT: ""
OPENPALETTE_PG_DBNAME: ""
OPENPALETTE_PG_USERNAME: ""
OPENPALETTE_PG_PASSWORD: ""

# 额外参数，未在原vnpm蓝图env参数中显式声明
OPENPALETTE_LOGSTASH_OEM_TCP_PORT: ""
OPENPALETTE_LOGSTASH_COS_TCP_PORT: ""
OPENPALETTE_LOGSTASH_UMF_TCP_PORT: ""
OPENPALETTE_LOGSTASH_UMF_UDP_PORT: ""
OPENPALETTE_LOGSTASH_SHIPPER_HTTP_PORT: ""
OPENPALETTE_LOGSTASH_SHIPPER_ADDRESS: ""
OPENPALETTE_LOGSTASH_SHIPPER_PORT: ""
caas_type: ""

serviceName:
  cwsm: "cwsm"

ports:
  cwsm: 8080

securityContext:
  privileged: false
  runAsNonRoot: true
  runAsUser: 1040
  runAsGroup: 1040
  allowPrivilegeEscalation: false

# 指定POD中挂载网络
annotations:
  networks: '{"version":"v1","ports": [
    {"attach_to_network": "lan","attributes": {"nic_name": "eth1","function": "std","nic_type": "normal","accelerate": "false","combinable": "true"}},
    {"attach_to_network": "net_api","attributes": {"nic_name": "eth0","function": "std","nic_type": "normal","accelerate": "false","combinable": "true"}}]}'
  k8s.v1.cni.cncf.io/networks: '[{"name":"net-api","namespace":"director", "interface":"eth1", "cni-args": {"dpdk": "false"}}]' 
# vnpm中params参数，由OKI统一刷新到spd文件中，直接定义在global属性中
global:
  OPENPALETTE_NAMESPACE: ranoss
  OPENPALETTE_MSB_IP: ""
  OPENPALETTE_MSB_PORT: 10081
  OPENPALETTE_MSB_ROUTER_IP: ""
  OPENPALETTE_MSB_ROUTER_PORT: 80
  OPENPALETTE_MSB_ROUTER_HTTPS_PORT: 443
  TZ: 'Asia/Shanghai'

idn:
  pbcEnabled: false